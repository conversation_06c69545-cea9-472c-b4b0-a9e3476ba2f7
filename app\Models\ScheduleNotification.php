<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ScheduleNotification extends Model
{
    use HasFactory, SoftDeletes;
    // protected $connection = 'client';

    protected $fillable = [
        'user_id',
        'title',
        'message',
        'link_name',
        'redirect_url',
        'type',
        'status',
        'schedule_type',
        'time',
        'start_date',
        'min_registration_period',
        'max_registration_period',
        'expiration',
        'read_at',
    ];

    protected $casts = [
        'start_date' => 'date',
        'time' => 'datetime',
        'expiration' => 'datetime',
        'min_registration_period' => 'integer',
        'max_registration_period' => 'integer',
        'read_at' => 'datetime',
    ];
}
