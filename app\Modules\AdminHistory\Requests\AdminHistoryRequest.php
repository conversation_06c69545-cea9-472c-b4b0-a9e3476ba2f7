<?php

namespace App\Modules\AdminHistory\Requests;

use App\Modules\AdminHistory\Services\AdminHistoryService;
use Illuminate\Foundation\Http\FormRequest;

class AdminHistoryRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'type' => ['nullable', 'string'],
            'date' => ['nullable', 'string', 'in:Today,Yesterday,Last 7 Days,Last 30 Days'],
        ];
    }

    public function show()
    {
        return AdminHistoryService::instance()->prepareAdminLogView($this);
    }
}
