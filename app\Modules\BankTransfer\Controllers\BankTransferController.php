<?php

namespace App\Modules\BankTransfer\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\BankTransfer\Services\BankTransferService;
use App\Modules\BankTransfer\Constants\BankTransferPurposeConstants;
use App\Modules\BankTransfer\Requests\BankTransferListRequest;
use Illuminate\Http\Request;
use Inertia\Inertia;

class BankTransferController extends Controller
{
    public function index(BankTransferListRequest $request)
    {
        $data = BankTransferService::instance()
            ->getIndexData(
            $request->only(
                'showItems', 
                'orderBy', 
                'purpose', 
                'referenceNumber', 
                'client',
                'name', 
                'company', 
                'status'
            )
        ); 

        return Inertia::render(
            'WireTransfer/WireTransferIndex', 
            $data
        );
    }

    public function verifyEdit(Request $request)
    {
        $data = BankTransferService::instance()->getVerifyData($request->id);

        if ($data['purpose'] == BankTransferPurposeConstants::MARKETPLACE_PAYMENT || $data['purpose'] == BankTransferPurposeConstants::OFFER_PAYMENT)
        {
            return Inertia::render('WireTransfer/WireTransferVerifyMarketplacePayment', $data);
        }

        return Inertia::render('WireTransfer/Edit', $data);
    }

    public function verifyUpdate(Request $request)
    {
        BankTransferService::instance()->verify($request->all());
        // dd($request->all());

        return redirect()->route('billing.wire.transfer');
    }

    public function rejectUpdate(Request $request)
    {        
        BankTransferService::instance()->reject($request->all());
        
        return redirect()->route('billing.wire.transfer');
    }
}
