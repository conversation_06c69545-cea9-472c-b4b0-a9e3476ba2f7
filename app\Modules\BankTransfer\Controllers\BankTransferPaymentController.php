<?php

namespace App\Modules\BankTransfer\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\BankTransfer\Requests\BankTransferPaymentApproveRequest;
use App\Modules\BankTransfer\Requests\BankTransferPaymentRejectRequest;
use App\Modules\BankTransfer\Services\BankTransferPaymentService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class BankTransferPaymentController extends Controller
{
    public function approve(BankTransferPaymentApproveRequest $request, int $id)
    {
        (new BankTransferPaymentService())->approve(
            $request->only('paidAmount', 'note', 'purpose'), 
            $id
        );

        return $this->handleRedirection($request->purpose, 'Bank Transfer Payment Approved' );
    }

    public function reject(BankTransferPaymentRejectRequest $request, int $id)
    {
        (new BankTransferPaymentService())->reject(
            $request->only('paidAmount', 'note', 'purpose'),
            $id
        );

        return $this->handleRedirection($request->purpose, 'Bank Transfer Payment Rejected');
    }

    private function handleRedirection(string $purpose, string $message)
    {    
        $purpose =  match ($purpose) 
        {
            'add credit',         => 'addCredit',
            'marketplace payment' => 'marketPlacePayment',
            'offer payment'       => 'offerPayment',
            default               => 'addCredit'
        };

        return redirect()->route(
            'billing.wire.transfer',
            [
                'purpose' => $purpose  // Add purpose as query parameter
            ]
        )
        ->with('successMessage', $message);

    }
}
