<?php

namespace App\Modules\BankTransfer\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class BankTransferListRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'showItems' => 
            [
                'nullable', 
                'integer'
            ],
            'orderBy'   =>
            [
                'nullable',
                'string',
                Rule::in(
                    [
                        "Reference Number:desc",
                        "Reference Number:asc",
                        "Client:desc", 
                        "Client:asc",
                        "Name:desc",
                        "Name:asc",
                        "Company:desc",
                        "Company:asc",
                        "Amount:desc",
                        "Amount:asc",
                        "Date Created:asc",
                        "Date Created:desc",
                        "Date Updated:asc",
                        "Date Updated:desc"
                    ]
                )
            ],
            'purpose'   =>
            [
                'nullable',
                'string',
                Rule::in(
                    [
                        'addCredit',
                        'offerPayment',
                        'marketPlacePayment'
                    ]
                )
            ],
            'status' => 
            [
                'nullable',
                'string',
                Rule::in(
                    [
                        "Pending",
                        "Verified",
                        "Unverified",
                        "Rejected"
                    ]
                )
            ],
            'referenceNumber' => ['nullable', 'string'],
            'client'          => ['nullable', 'string'],
            'accountName'     => ['nullable', 'string'],
            'company'         => ['nullable', 'string'],
        ];
    }
}
