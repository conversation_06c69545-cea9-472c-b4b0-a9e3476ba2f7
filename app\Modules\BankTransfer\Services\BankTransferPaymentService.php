<?php

namespace App\Modules\BankTransfer\Services;

use App\Mail\BankTransferPaymentApprovalMail; 
use App\Mail\BankTransferPaymentRejectionMail;
use App\Modules\Client\Services\ClientNotificationService;
use App\Modules\BankTransfer\Constants\BankTransferStatus;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\AdminHistory\Constants\HistoryType;
use App\Modules\BankTransfer\Constants\BankTransferInvoiceStatusConstants;
use App\Modules\BankTransfer\Constants\BankTransferPurposeConstants;
use App\Modules\BillingClient\Services\MarketInvoiceService;
use App\Modules\MarketPlace\Constants\AfternicOfferConstants;
use Exception;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class BankTransferPaymentService
{
    /**
     * APPROVE BANK TRANSFER PAYMENT
     * 
     * @param array $data 
     * @param int   $bankTransferId
     * 
     * @return void
     */
    public function approve(array $data, int $bankTransferId) : void 
    {
        $isOffer = $this->checkoutIfOffer($bankTransferId);

        try 
        {
            //! GET PAYMENT SERVICE ENTRY 
            $paymentService = DB::client()
                ->table('payment_services')
                ->where('bank_transfer_id', '=', $bankTransferId)
                ->first();

            DB::transaction(
                function () use ($data, $bankTransferId, $paymentService, $isOffer)
                {
                    //! CHECK IF OFFER THEN UPDATE TO PAID 
                    if ($isOffer)
                    {
                        $this->updateOfferStatus($paymentService->id, AfternicOfferConstants::PAID_TRANSFER_PENDING); 
                    }   

                    //! SET TO VERIFIED 
                    DB::client()
                        ->table('bank_transfers')
                        ->where('id', '=', $bankTransferId)
                        ->update(
                            [
                                'verified_at' => now(),
                                'note'        => $data['note']
                            ]
                        );

                    //! GET MARKET PLACE INVOICE & SET IT TO BANK TRANSFER APPROVED PROCESSING 
                    DB::client() 
                        ->table('market_place_payment_invoices')
                        ->where('payment_service_id', '=', $paymentService->id)
                        ->update(
                            [
                                'status' => BankTransferInvoiceStatusConstants::BANK_TRANSFER_PAYMENT_APPROVED_PROCESSING
                            ]
                        );

                    //! CREATE NOTIFICATION FOR CLIENT
                    (new ClientNotificationService())->createClientNotification(
                        $paymentService->user_id,
                        'Bank Transfer Approved',
                        'Your bank transfer request has been Approved.',
                        'important',
                        '/wire-transfer'
                    );

                }
            );
    
            $data['paymentServiceId'] = $paymentService->id;
            $data['bankTransferId']   = $bankTransferId;
            $data['clientId']         = $paymentService->user_id; 

            //! SEND APPROVAL EMAIL TO CLIENT 
            $this->sendEmailToClient(
                $data,
                $paymentService->user_id,
                'APPROVAL',
            );
        }
        catch (Exception $error)
        {
            app(AuthLogger::class)->error("Bank Transfer entry {$bankTransferId} Approval Failed {$error->getMessage()}");
            throw $error; 
        }
    }

    /**
     * REJECT BANK REJECT PAYMENT
     * 
     * @param array $data 
     * @param int   $bankTransferId
     * 
     * @return void
     */
    public function reject(array $data,  int $bankTransferId)
    {
        $isOffer = $this->checkoutIfOffer($bankTransferId); 

        try 
        {
            //! GET PAYMENT SERVICE ENTRY 
            $paymentService = DB::client()
                ->table('payment_services')
                ->where('bank_transfer_id', '=', $bankTransferId)
                ->first();

            DB::transaction(
                function () use ($data, $bankTransferId, $paymentService, $isOffer)
                {
                    //! CHECK IF OFFER THEN UPDATE TO CLOSED 
                    if ($isOffer)
                    {
                        $this->updateOfferStatus($paymentService->id, AfternicOfferConstants::CLOSED); 
                    }   

                    //! SET TO REJECTED 
                    DB::client()
                        ->table('bank_transfers')
                        ->where('id', '=', $bankTransferId)
                        ->update(
                            [
                                'deleted_at' => now(),
                                'note'       => $data['note']
                            ]
                        );

                    //! GET MARKET PLACE INVOICE & SET IT TO BANK TRANSFER REJECTION PROCESSING 
                    DB::client()
                        ->table('market_place_payment_invoices')
                        ->where('payment_service_id', '=', $paymentService->id)
                        ->update(
                            [
                                'status' => BankTransferInvoiceStatusConstants::BANK_TRANSFER_PAYMENT_REJECTED_PROCESSING
                            ]
                        );

                    //! CREATE NOTIFICATION FOR CLIENT
                    (new ClientNotificationService())->createClientNotification(
                        $paymentService->user_id,
                        'Bank Transfer Rejected',
                        'Your bank transfer request has been Rejected. Please contact Support',
                        'important',
                        '/wire-transfer'
                    );
                }
            );

            $data['paymentServiceId'] = $paymentService->id;
            $data['bankTransferId']   = $bankTransferId;
            $data['clientId']         = $paymentService->user_id;

            //! SEND REJECTION EMAIL TO CLIENT
            $this->sendEmailToClient(
                $data,
                $paymentService->user_id,
                'REJECTION',
            );
        }
        catch (Exception $error)
        {
            app(AuthLogger::class)->error("Bank Transfer entry {$bankTransferId} Rejection Failed {$error->getMessage()}");
            throw $error;
        }    
    } 

    //! PRIVATE FUNCTIONS 

    /**
     * CHECK IF OFFER 
     * 
     * @param int $bankTransferId
     * 
     * @return bool 
     */
    private function checkoutIfOffer(int $bankTransferId) : bool
    {
        $bankTransfer = DB::client()
            ->table('bank_transfers')
            ->where('id', '=', $bankTransferId)  
            ->first(); 

        $isOffer = false; 

        if ($bankTransfer->purpose == BankTransferPurposeConstants::OFFER_PAYMENT)
        {
            $isOffer = true; 
        } 

        return $isOffer;
    }

    /**
     * UPDATE OFFER STATUS 
     * 
     * @param int    $paymentServiceId
     * @param string $status
     * 
     * @return void 
     */
    public function updateOfferStatus(int $paymentServiceId, string $status)
    {
        $orders = DB::client()->table('market_place_payment_invoices')
            ->join('market_place_node_invoices', 'market_place_node_invoices.marketplace_payment_invoice_id', '=', 'market_place_payment_invoices.id')
            ->join('market_place_domains', 'market_place_domains.id', '=', 'market_place_node_invoices.marketplace_payment_node_id')
            ->join('registered_domains', 'registered_domains.id', '=', 'market_place_domains.registered_domain_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('payment_services', 'payment_services.id', '=', 'market_place_payment_invoices.payment_service_id')
            ->join('users', 'users.id', '=', 'payment_services.user_id')
            ->where('market_place_payment_invoices.payment_service_id', $paymentServiceId)
            ->select(
                'market_place_domains.vendor as vendor',
                'domains.id as domainId',
                'domains.name as domainName',
                'users.id as userId',
            )
            ->get();

        foreach ($orders as $order)
        {
            switch ($order->vendor)
            {
                case 'afternic': 
                    DB::client()
                        ->table('afternic_offers')
                        ->where('domain_name', '=', $order->domainName)
                        ->where('user_id', '=', $order->userId)
                        ->update(
                            [
                                'offer_status' => $status
                            ]
                        );
                    break; 

                default : 
            }
        }
    }

    /**
     * SEND EMAIL TO CLIENT 
     * 
     * @param array  $data 
     * @param int    $userId 
     * @param string $type 
     * 
     * @return void
     */
    private function sendEmailToClient(array $data, int $userId, string $type = 'APPROVAL')
    {
        $user = DB::client()
            ->table('users')
            ->where('id', '=', $userId)
            ->select('id', 'email', 'first_name', 'last_name')
            ->first();
    
        if ($type == 'APPROVAL')
        {
            Mail::to($user->email)->queue(new BankTransferPaymentApprovalMail($data));
        }
        else 
        {
            Mail::to($user->email)->queue(new BankTransferPaymentRejectionMail($data));
        }
    }
}
