<?php

namespace App\Modules\BankTransfer\Services;

use App\Exceptions\FailedRequestException;
use App\Events\AdminActionEvent;
use App\Modules\AdminHistory\Constants\HistoryType;
use App\Modules\BankTransfer\Constants\BankTransferPurposeConstants;
use App\Modules\BillingClient\Services\MarketInvoiceService;
use App\Traits\CursorPaginate;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class BankTransferService
{
    use CursorPaginate;

    private $pageLimit = 10;

    private const VERIFY = 'verify';

    private const REVERSE = 'reverse';

    private const UNVERIFIED = 'unverified';

    public static function instance(): self
    {
        $bankTransferService = new self;

        return $bankTransferService;
    }

    public function getIndexData(array $data)
    {
        $pageLimit = $data['showItems'] ?? 10;

        $validPurposes = ['addCredit', 'marketPlacePayment', 'offerPayment'];

        //! Determine the effective purpose, defaulting to 'add credit'
        $purpose = isset($data['purpose']) && in_array($data['purpose'], $validPurposes)
            ? $data['purpose']
            : 'addCredit';

        $purpose =  match ($purpose) 
        {
            'addCredit'          => 'add credit',
            'marketPlacePayment' => 'marketplace payment',
            'offerPayment'       => 'offer payment',
            default              => 'add credit'
        };

        $orderBy = 
        [
            "value"     => 'bank_transfers.id',
            "direction" => 'desc'
        ]; 
                
        if (isset($data['orderBy']))
        {
            $dataOrderBy = explode(':', $data['orderBy']);

            switch ($dataOrderBy[0]) 
            {
                case 'Reference Number':
                    $orderBy["value"] = 'bank_transfers.reference_number';
                    break;
                case 'Client':
                    $orderBy["value"] = 'users.email';
                    break;
                case 'Name':
                    $orderBy["value"] = 'bank_transfers.account_name';
                    break;
                case 'Company':
                    $orderBy["value"] = 'bank_transfers.company';
                    break;
                case 'Amount':
                    $orderBy["value"] = 'bank_transfers.gross_amount';
                    break;
                case 'Date Created':
                    $orderBy["value"] = 'bank_transfers.created_at';
                    break;
            }

            $orderBy['direction'] = $dataOrderBy[1];
        }
        
        $builder = DB::client() 
            ->table('bank_transfers')
            ->select(
                'bank_transfers.id as bankTransferId', 
                'bank_transfers.reference_number as bankTransferReferenceNumber',
                'bank_transfers.gross_amount as bankTransferGrossAmount', 
                'bank_transfers.purpose as bankTransferPurpose',  
                'bank_transfers.account_name as bankTransferAccountName', 
                'bank_transfers.company as bankTransferCompany', 
                'bank_transfers.note as bankTransferNote',
                'bank_transfers.created_at as bankTransferCreatedAt', 
                'bank_transfers.updated_at as bankTransferUpdatedAt',
                'bank_transfers.deleted_at as bankTransferDeletedAt', 
                'bank_transfers.verified_at as bankTransferVerifiedAt',
                'bank_transfers.reviewed_at as bankTransferReviewedAt',
                'users.email as userEmail',
                'users.id as userId',
            )
            ->leftJoin('users', 'users.id', '=', 'bank_transfers.user_id')
            ->where('purpose', '=', $purpose)
            ->when(
                isset($data['referenceNumber']),
                function ($query) use ($data)
                {
                    $query->where('bank_transfers.reference_number', 'like', $data['referenceNumber'] . '%');
                }
            )
            ->when(
                isset($data['client']),
                function ($query) use ($data)
                {
                    $query->where('users.email', 'like', $data['client'] . '%');
                }
            )
            ->when(
                isset($data['name']),
                function ($query) use ($data)
                {
                    $query->where('bank_transfers.account_name', 'like', $data['name'] . '%');
                }
            )
            ->when(
                isset($data['company']),
                function ($query) use ($data)
                {
                    $query->where('bank_transfers.company', 'like', $data['company'] . '%');
                }
            )
            ->when(
                isset($data['status']), 
                function ($query) use ($data)
                {
                    switch ($data['status']) 
                    {
                        case 'Pending':
                            $query->whereNull('bank_transfers.verified_at')->whereNull('bank_transfers.reviewed_at')->whereNull('bank_transfers.deleted_at');
                            break;
                        case 'Verified':
                            $query->whereNotNull('bank_transfers.verified_at');
                            break;
                        case 'Unverified':
                            $query->whereNotNull('bank_transfers.reviewed_at');
                            break;
                        case 'Rejected':
                            $query->whereNotNull('bank_transfers.deleted_at');
                            break;
                    }
                }
            )
            ->orderBy($orderBy['value'], $orderBy['direction'])
            ->paginate($pageLimit)
            ->withQueryString();

            if (
                $purpose == 'marketplace payment'
                || $purpose == 'offer payment'
            )
            {
                foreach ($builder as $item)
                {
                    //! GET DOMAINS 
                    $domains = $this->getEntryDomains($item->bankTransferId, $item->userId); 
    
                    //! ADD Domains to Builder 
                    $item->domains = $domains;
                }
            }

        return CursorPaginate::cursor(
            $builder, 
            $this->paramToURI($data)
        );
    }

    /**
     * GET ENTRY DOMAINS 
     * 
     * @param int $bankTransferId 
     * @param int $userId 
     * 
     * @return array
     */
    private function getEntryDomains (int $bankTransferId, int $userId) : array
    {   
        $paymentService = DB::client()->table('payment_services')
            ->select(
                'payment_services.id'
            ) 
            ->where('bank_transfer_id', '=', $bankTransferId)
            ->where('user_id', '=', $userId)
            ->first();
    
        $domains = DB::client()->table('market_place_payment_invoices')
            ->join('market_place_node_invoices', 'market_place_node_invoices.marketplace_payment_invoice_id', '=', 'market_place_payment_invoices.id')
            ->join('market_place_domains', 'market_place_domains.id', '=', 'market_place_node_invoices.marketplace_payment_node_id')
            ->join('registered_domains', 'registered_domains.id', '=', 'market_place_domains.registered_domain_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->where('market_place_payment_invoices.payment_service_id', $paymentService->id)
            ->where('market_place_domains.user_id', $userId)
            ->select(
                'domains.name as domainName',
            )
            ->pluck('domainName')
            ->toArray();

        return $domains; 
    }

    public function query()
    {
        $query = DB::client()->table('payment_services')
            ->join('bank_transfers', 'bank_transfers.id', '=', 'payment_services.bank_transfer_id')
            ->join('users', 'users.id', '=', 'payment_services.user_id')
            ->whereNull('payment_services.account_credit_id')
            ->select(
                'bank_transfers.*',
                'bank_transfers.verified_at as status',
                'bank_transfers.gross_amount as amount',
                'payment_services.id as payment_service_id',
                'users.id as user_id',
                'users.first_name',
                'users.last_name',
                'users.email',
            );

        return $this->applyFilters($query);
    }

    private function applyFilters($query)
    {
        $query = $this->applyStatusFilter($query);
        $query = $this->applyOrderByFilter($query);
        $query = $this->applyNameFilter($query);
        $query = $this->applyCompanyFilter($query);
    
        return $query;
    }

    private function applyCompanyFilter($query) 
    {
        if(request()->has('company')){
            $query->where('company','like',request()->company.'%');
        }
        return $query;
    }

    private function applyNameFilter($query)
    {
        if (request()->has('name')) {
            $query->where('account_name', 'like',request()->name .'%');
        }
        return $query;
    }

    private function applyStatusFilter($query)
    {
        if (request()->has('status')) {
            $status = request()->status;

            switch ($status) {
                case 'Pending':
                    $query->whereNull('bank_transfers.verified_at')->whereNull('bank_transfers.reviewed_at')->whereNull('bank_transfers.deleted_at');
                    break;
                case 'Verified':
                    $query->whereNotNull('bank_transfers.verified_at');
                    break;
                case 'Unverified':
                    $query->whereNotNull('bank_transfers.reviewed_at');
                    break;
                case 'Rejected':
                    $query->whereNotNull('bank_transfers.deleted_at');
                    break;
            }
        }
        return $query;
    }

    private function applyOrderByFilter($query)
    {
        if (request()->has('orderby')) {
            $orderBy = request()->orderby;
            switch ($orderBy) {
                case 'Date Created: Desc':
                    $query->orderBy('bank_transfers.created_at', 'desc');
                    break;
                case 'Date Created: Asc':
                    $query->orderBy('bank_transfers.created_at', 'asc');
                    break;
                case 'Amount: Desc':
                    $query->orderBy('bank_transfers.gross_amount', 'desc');
                    break;
                case 'Amount: Asc':
                    $query->orderBy('bank_transfers.gross_amount', 'asc');
                    break;
                case 'Date Updated: Desc':
                    $query->orderBy('bank_transfers.updated_at', 'desc');
                    break;
                case 'Date Updated: Asc':
                    $query->orderBy('bank_transfers.updated_at', 'asc');
                    break;
                case 'Received Amount: Desc':
                    $query->orderBy('bank_transfers.net_amount', 'desc');
                    break; 
                case 'Received Amount: Asc':
                    $query->orderBy('bank_transfers.net_amount', 'asc');
                    break;
                case 'Name: Asc':
                    $query->orderBy('bank_transfers.account_name', 'asc');
                    break;
                case 'Name: Desc':
                    $query->orderBy('bank_transfers.account_name', 'desc');
                    break;
                default:
                    $query->orderBy('bank_transfers.id', 'desc');
                    break;
            }
        } else {
            $query->orderBy('bank_transfers.id', 'desc');
        }

        return $query;
    }

    public function getAll()
    {
        return $this->query()->get()->all();
    }

    public function getVerifyData(int $id)
    {
        $item = $this->query()->where('bank_transfers.id', $id)->get()->first();

        if ($item->verified_at != null) {
            throw new FailedRequestException(400, 'Bad request.', 'Cannot verify transaction.');
        }

        if ($item->purpose == BankTransferPurposeConstants::MARKETPLACE_PAYMENT || $item->purpose == BankTransferPurposeConstants::OFFER_PAYMENT)  
        {
            $marketPlaceInvoiceId = DB::client()->table('market_place_payment_invoices')
                ->where('payment_service_id', '=', $item->payment_service_id)
                ->first()
                ->id;

            $marketPlaceInvoice = MarketInvoiceService::instance()->getMarketPlaceInvoice($marketPlaceInvoiceId, $item->user_id);

            return [
                'payment' => $item,
                'action' => self::VERIFY,
                'purpose' => $item->purpose,
                'invoiceOrders' => $marketPlaceInvoice
            ];
        }

        return [
            'item' => $item,
            'action' => self::VERIFY,
            'purpose' => $item->purpose
        ];
    }

    public function verify(array $request)
    {
        $bankTransfer = $this->query()->where('bank_transfers.id', $request['bank_transfer_id'])->get()->first();

        if (
            $bankTransfer->verified_at != null ||
            $bankTransfer->credited_at != null
        ) {
            throw new FailedRequestException(400, 'Bad request.', 'Cannot verify transaction.');
        }

        $netAmount = floatval($request['net_amount']);
        $this->validateNetAmount($bankTransfer->gross_amount, $netAmount);
        $serviceFee = $bankTransfer->gross_amount - $netAmount;
        $verifiedAt = $request['is_verified'] ? now() : null;

        $data = [
            'net_amount'   => $netAmount ?? $bankTransfer->gross_amount,
            'service_fee'  => $serviceFee ?? 0,
            'note'         => $request['note'] ?? '',
            'verified_at'  => $verifiedAt,
            'reviewed_at'  => null,
            'retrieved_at' => null,
            'admin_id'     => Auth::id(),
            'updated_at'   => now(),
        ];

        DB::client()->table('bank_transfers')->where('id', $request['bank_transfer_id'])->update($data);

        $message = "Bank transfer id: {$request['bank_transfer_id']} verified for user {$bankTransfer->email} - Amount: $" . number_format($netAmount, 2) . " by " . Auth::user()->email;
        event(new AdminActionEvent(Auth::id(), HistoryType::BANK_TRANSFER_VERIFIED, $message));
    }


    public function reject(array $request)
    {
        $bankTransfer = $this->query()->where('bank_transfers.id', $request['bank_transfer_id'])->get()->first();

        if (
            $bankTransfer->credited_at != null ||
            $bankTransfer->deleted_at != null
        ) {
            throw new FailedRequestException(400, 'Bad request.', 'Cannot reject transaction.');
        }

        // $netAmount = floatval($request['net_amount']);
        // $serviceFee = $bankTransfer->gross_amount - $netAmount;
        $isFirstAttempt = $bankTransfer->reviewed_at === null && $bankTransfer->deleted_at === null;

        $data = 
        [
            // 'net_amount'   => $netAmount ?? 0,
            // 'service_fee'  => $serviceFee ?? 0,
            'note'         => $request['note'] ?? '',
            'reviewed_at'  => $isFirstAttempt ? now() : null,
            'deleted_at'   => $isFirstAttempt ? null : now(),
            'retrieved_at' => null,
            'admin_id'     => Auth::id(),
        ];


        DB::client()->table('bank_transfers')->where('id', $request['bank_transfer_id'])->update($data);

        $this->notifyBankTransferReject($bankTransfer->user_id, $isFirstAttempt);

        $action = $isFirstAttempt ? 'rejected' : 'permanently rejected';
        $message = "Bank transfer id: {$request['bank_transfer_id']} {$action} for user {$bankTransfer->email} by " . Auth::user()->email;
        event(new AdminActionEvent(Auth::id(), HistoryType::BANK_TRANSFER_REJECTED, $message));
    }

    // PRIVATE FUNCTIONS
    private function paramToURI(array $data)
    {
        $params = [];

        if (isset($data['referenceNumber'])) 
        {
            $params[] = 'referenceNumber=' . $data['referenceNumber'];
        }

        if (isset($data['accountName'])) 
        {
            $params[] = 'accountName=' . $data['accountName'];
        }

        if (isset($data['company'])) 
        {
            $params[] = 'company=' . $data['company'];
        }

        if (isset($data['orderBy'])) 
        {
            $params[] = 'orderBy=' . $data['orderBy'];
        }

        return $params;
    }

    private function notifyBankTransferReject(int $userId, bool $isFirstAttempt)
    {
        if ($isFirstAttempt) {
            $this->notify($userId, 'Bank Transfer Unverified', 'Your bank transfer request has been Unverified. Please contact support.', 'important');
        } else {
            $this->notify($userId, 'Bank Transfer Rejected', 'Your bank transfer request has been rejected.', 'critical');
        }
    }

    private function notify(int $userId, string $title, string $message, string $importance)
    {
        $data = [
            'user_id' => $userId,
            'title' => $title,
            'message' => $message,
            'importance' => $importance,
            'redirect_url' => '/wire-transfer',
            'read_at' => null,
            'created_at' => now(),
            'updated_at' => now(),
        ];

        DB::client()->table('notifications')->insert($data);
    }

    private function validateNetAmount(float $grossAmount, float $netAmount)
    {
        if ($netAmount <= 0) {
            throw new FailedRequestException(400, 'Bad request.', 'Net amount cannot be zero.');
        }

        if ($netAmount > $grossAmount) {
            throw new FailedRequestException(400, 'Bad request.', 'Net amount cannot be greater than gross amount.');
        }

        $threshold = $grossAmount * 0.2;

        if ($netAmount < $threshold) {
            throw new FailedRequestException(400, 'Bad request.', 'Net amount cannot be less than 20% of gross amount.');
        }
    }
}
