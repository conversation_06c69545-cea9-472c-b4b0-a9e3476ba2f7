<?php

namespace App\Modules\BillingClient\Constants;

final class PaymentSummaryType
{
    public const PAYMENT_INVOICE = 'PAYMENT_INVOICE';

    public const PAYMENT_REIMBURSEMENT = 'PAYMENT_REIMBURSEMENT';

    public const MARKETPLACE_INVOICE = 'MARKETPLACE_INVOICE';

    public const MARKETPLACE_REIMBURSEMENT = 'MARKETPLACE_REIMBURSEMENT';

    public const ACCOUNT_BALANCE = 'ACCOUNT_BALANCE';

    public const MULTI_CHECKOUT_INVOICE = 'MULTI_CHECKOUT_INVOICE';

    public const ALL = [
        self::PAYMENT_INVOICE,
        self::PAYMENT_REIMBURSEMENT,
        self::MARKETPLACE_INVOICE,
        self::MARKETPLACE_REIMBURSEMENT,
        self::ACCOUNT_BALANCE,
        self::MULTI_CHECKOUT_INVOICE,
    ];

    public const TEXT = [
        self::PAYMENT_INVOICE => 'Payment Invoice',
        self::PAYMENT_REIMBURSEMENT => 'Payment Reimbursement',
        self::MARKETPLACE_INVOICE => 'Marketplace Invoice',
        self::MARKETPLACE_REIMBURSEMENT => 'Marketplace Reimbursement',
        self::ACCOUNT_BALANCE => 'Account Balance',
    ];
}
