<?php

namespace App\Modules\Client\Requests;

use Illuminate\Foundation\Http\FormRequest;

class DomainLogRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'type' => 'nullable|string',
            'date' => 'nullable|string|in:Today,Yesterday,Last 7 Days,Last 30 Days',
            'domain' => 'nullable|string'
        ];
    }

    public function filters(): array
    {
        return array_filter($this->only([
            'type',
            'date',
            'domain'
        ]));
    }
} 