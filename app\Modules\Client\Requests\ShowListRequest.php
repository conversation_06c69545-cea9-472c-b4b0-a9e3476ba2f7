<?php

namespace App\Modules\Client\Requests;

use App\Modules\Client\Services\ClientService;
use App\Modules\Client\Services\DomainService;
use App\Modules\Setting\Services\ExtensionFeeService;
use App\Rules\QueryStringInArray;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Modules\Client\Services\ClientLogService;
use App\Modules\Setting\Services\TransactionThresholdService;

class ShowListRequest extends FormRequest
{
    protected $redirect = 'bad-request';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */

    // Only specific query values allowed

    public function rules(): array
    {
        return self::rulesOnPath();
    }

    private function rulesOnPath(): array
    {
        $path = $this->getPathInfo();

        if (str_contains($path, 'client') && str_contains($path, 'domains')) { // client/domains page
            return [
                'status' => ['string', new QueryStringInArray('Active,Expired,Transferred')],
                'orderby' => ['string', Rule::in([
                    'domain:desc',
                    'domain:asc',
                    'expiry:desc',
                    'expiry:asc',
                    'created:desc',
                    'created:asc',
                ])],
                'tld' => ['string', new QueryStringInArray('com,net,org')],
                'minExpireDate' => ['string'],
                'maxExpireDate' => ['string'],
            ];
        }

        if (str_contains($path, 'client')) { // client page
            return [
                'orderby' => ['string', Rule::in([
                    'created:desc',
                    'created:asc',
                    'name:desc',
                    'name:asc',
                    'Email: Desc',
                    'Email: Asc',
                    'Last Active: Desc',
                    'Last Active: Asc',
                ])],
                'status' => ['string', new QueryStringInArray(values: 'Enabled,Disabled,Pending')],
                'email' => ['string'],
            ];
        }

        return [];
    }

    public function show()
    {
        return ClientService::get($this);
    }

    public function extension_fees_show()
    {
        $data = ClientService::get($this);

        $user_ids = [];
        foreach ($data['items'] as $item) {
            $user_ids[] = $item->id;
        }
        // dd($data['items']);
        // dd($user_ids);
        $data['fees'] = ExtensionFeeService::instance()->getUsersFeeByUserIds($user_ids);

        // ddd($data);
        return $data;
    }

    public function showUsers()
    {
        $data = ClientService::get($this);
        // dd($data);
        return $data;
    }


    public function domains_get(string $id)
    {
        $data = DomainService::getUserDomain($this, $id);

        return $data;
    }

    public function get_domains_owner(string $id)
    {
        $data = DomainService::getDomainsOwner($id);

        return $data;
    }

    public function get_owner_exists(string $id)
    {
        $data = DomainService::checkOwnerExists($id);

        return $data;
    }
}
