<?php

namespace App\Modules\Client\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Traits\GroupDateFormatting;
use Carbon\Carbon;

class SystemLogRequest extends FormRequest
{
    use GroupDateFormatting;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'type' => ['nullable', 'string'],
            'date' => ['nullable', 'string', 'in:Today,Yesterday,Last 7 Days,Last 30 Days'],
            'cursor' => ['nullable', 'string'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            // Remove user_id related messages
        ];
    }
}
