<?php

namespace App\Modules\Client\Services;

use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Modules\Client\Requests\DomainLogRequest;
use Illuminate\Database\Query\Builder;
use App\Modules\DomainHistory\Constants\TransactionType;
use App\Traits\CursorPaginate;
use App\Traits\GroupDateFormatting;

class ClientDomainLogService
{
    use CursorPaginate, GroupDateFormatting;
    
    private const PAGE_LIMIT = 20;

    /**
     * Build base query for domain logs
     */
    private function buildBaseQuery(?int $userId = null): Builder
    {
        $query = DB::table('public.domain_transaction_histories as dth')
            ->join('public.domains as d', 'd.id', '=', 'dth.domain_id')
            ->join('public.users as u', 'u.id', '=', 'dth.user_id')
            ->select([
                'dth.id',
                'dth.domain_id',
                'd.name as domain_name',
                'dth.type',
                'dth.created_at',
                'dth.status',
                'dth.message',
                'dth.payload',
                'u.email'
            ])
            ->orderBy('dth.id', 'desc');

        if ($userId) {
            $query->where('dth.user_id', $userId);
        }

        return $query;
    }

     /**
     * Get user email
     */
     private function getUserEmail(?int $userId): ?string
     {
         if (!$userId) {
             return null;
         }
         return DB::table('public.users')
             ->where('id', $userId)
             ->value('email');
     }
 
    
    /**
     * Get all domain logs data for the view
     */
    public function getDomainLogsData(?int $userId, DomainLogRequest $request): array
    {
        $query = $this->buildBaseQuery($userId);
        $this->applyFilters($query, $request);

        $pageLimit = $request->input('limit', 20);
        
        $logs = $query->paginate($pageLimit)
            ->appends($request->filters())->withQueryString();
            
        $formattedLogs = $this->formatLogs($logs);
        $paginatedData = self::cursor($formattedLogs, $request->filters());
        
        return [
            'email' => $this->getUserEmail($userId),
            'user_id' => $userId,
            'logs' => $paginatedData['items'],
            'onFirstPage' => $paginatedData['onFirstPage'],
            'onLastPage' => $paginatedData['onLastPage'],
            'nextPageUrl' => $paginatedData['nextPageUrl'],
            'previousPageUrl' => $paginatedData['previousPageUrl'],
            'itemCount' => $paginatedData['itemCount'],
            'total' => $paginatedData['total'],
            'filters' => $request->validated(),
            'statuses' => TransactionType::getConstants(),
        ];
    }

    /**
     * Apply filters to query
     */
    private function applyFilters(Builder $query, DomainLogRequest $request): void
    {
        if ($request->filled('type')) {
            $query->where('dth.type', $request->input('type'));
        }

        if ($request->filled('date')) {
            $this->applyDateFilter($query, $request->input('date'));
        }

        if ($request->filled('domain')) {
            $query->where('d.name', 'LIKE', "{$request->input('domain')}%");
        }

        if ($request->filled('email')) {
            $query->where('u.email', 'LIKE', "{$request->input('email')}%");
        }
    }

    /**
     * Format logs with pagination
     */
    private function formatLogs($logs)
    {
        $logs->through(function ($log) {
            return $this->formatLogEntry($log);
        });

        return $logs;
    }

    /**
     * Format individual log entry
     */
    private function formatLogEntry($log): object
    {
        $log->type = TransactionType::getConstants()[$log->type] ?? $log->type;
        $log->formatted_created_at = $log->created_at ? $this->formatDate($log->created_at) : 'No date available';
        $log->message = $log->message ?? null;
        return $log;
    }

    /**
     * Apply date filter to query
     */
    private function applyDateFilter(Builder $query, string $dateFilter): void
    {
        $filters = [
            'Today' => fn() => $query->whereDate('dth.created_at', Carbon::today()),
            'Yesterday' => fn() => $query->whereDate('dth.created_at', Carbon::yesterday()),
            'Last 7 Days' => fn() => $query->whereBetween('dth.created_at', [Carbon::now()->subDays(7), Carbon::now()->subDays(1)]),
            'Last 30 Days' => fn() => $query->where('dth.created_at', '>=', Carbon::now()->subDays(30))
        ];

        if (isset($filters[$dateFilter])) {
            $filters[$dateFilter]();
        }
    }
    
    /**
     * Get log payload data for a specific log entry
     *
     */
    public function getLogPayload($request)
    {
        $id = $request->input('id');
        $log = $this->buildBaseQuery()->where('dth.id', $id)->first();

        if (!$log) {
            return null;
        }

        return json_decode($log->payload);
    }
}
