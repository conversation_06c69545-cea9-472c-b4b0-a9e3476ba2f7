<?php

namespace App\Modules\Client\Services;

use App\Modules\AdminCredit\Services\AdminCreditService;
use App\Modules\Client\Constants\DomainStatus;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Traits\CursorPaginate;
use Carbon\Carbon;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;
use App\Events\AdminActionEvent;
use App\Modules\AdminHistory\Constants\HistoryType;

class ClientService
{
    use CursorPaginate;

    private static $pageLimit = 20;

    public static function get($request)
    {
        $builder = DB::client()->table('users')
            ->leftJoin('user_ips', 'user_ips.user_id', 'users.id')
            ->leftJoin('ips', 'ips.id', '=', 'user_ips.ip_id')
            ->whereNull('users.deleted_at');

        // $builder = DB::client()->table('users')
        //     ->leftJoin('user_ip_address_sessions', 'user_ip_address_sessions.user_id', '=', 'users.id')
        //     ->leftJoin('ips', 'ips.id', '=', 'user_ip_address_sessions.ip_id')
        //     ->whereNull('users.deleted_at');

        self::whenHasEmail($builder, $request);
        self::whenHasStatus($builder, $request);
        $builder = $builder->groupBy('users.id');
        self::$pageLimit = $request->input('limit', 20);
        self::whenHasOrderby($builder, $request);
        $builder = $builder->select('users.*', DB::raw("string_agg(ips.ip, ',') as ip"))
            ->paginate(self::$pageLimit)->withQueryString();

        self::appendUserDomainCount($builder);

        return CursorPaginate::cursor($builder, self::paramToURI($request));
    }

    private static function appendUserDomainCount(&$builder)
    {
        foreach ($builder->items() as $item) {
            $domainCount = DB::client()->table('domains')
                ->join('registered_domains', 'registered_domains.domain_id', '=', 'domains.id')
                ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
                ->where('user_contacts.user_id', $item->id)
                ->where('registered_domains.status', DomainStatus::OWNED)
                ->count();
            $item->domain_count = $domainCount;
        }
    }

    private static function whenHasEmail(&$builder, $request)
    {
        $builder->when($request->has('email'), function (Builder $query) use ($request) {
            $email = $request->email;
            $query->where('users.email', 'ilike', $email . '%');
        });
    }

    private static function whenHasStatus(&$builder, $request)
    {
        $builder->when($request->has('status'), function (Builder $query) use ($request) {
            $status = $request->status;

            if ($status === 'Pending') {
                $query->where('users.is_invited', true)
                    ->where('users.is_active', false);
            }
            if ($status === 'Enabled') {
                $query->where('users.is_active', true);
            }
            if ($status === 'Disabled') {
                $query->where('users.is_active', false)
                    ->where('users.is_invited', false);
            }
        });
    }

    private static function whenHasOrderby(&$builder, $request)
    {
        $builder->when($request->has('orderby'), function (Builder $query) use ($request) {
            $orderby = explode(':', $request->orderby);

            $orderby[1]=trim($orderby[1]);

            switch ($orderby[0]) {
                case 'Created':
                    $query->orderBy('users.created_at', $orderby[1]);
                    break;
                case 'Name':
                    $query->orderBy('users.last_name', $orderby[1]);
                    break;
                case 'Email':
                    $query->orderBy('users.email', $orderby[1]);
                    break;
                case 'Last Active':
                    // $query->orderBy('users.last_active_at', $orderby[1]);
                    $query->orderByRaw("(NOW() - users.last_active_at)::interval " . $orderby[1]);
                    break;
            }
        })
            ->when(! $request->has('orderby'), function (Builder $query) {
                $query->orderBy('users.created_at', 'desc');
            });
    }

    private static function paramToURI($request)
    {
        $param = [];

        if ($request->has('status')) {
            $param[] = 'status=' . $request->status;
        }

        if ($request->has('orderby')) {
            $param[] = 'orderby=' . $request->orderby;
        }

        if ($request->has('email')) {
            $param[] = 'email=' . $request->email;
        }

        return $param;
    }

    public static function update(array $ids, bool $status, $is_invited)
    {
        foreach ($ids as $id) {
            $data = [
                'is_active' => $status,
                'updated_at' => now(),
            ];

            if (!is_null($is_invited)) {
                $data['is_invited'] = $is_invited;
            }

            self::changeStatus($id, $data);
        }

        app(AuthLogger::class)->info('update status of ' . implode(',', $ids) . ' to ' . $status);
    }

    public static function softDelete(array $ids, $desc = 'Debit fund from cancel invite for email: ')
    {
        self::logClientDeletionEvent($ids);

        foreach ($ids as $id) {
            self::changeStatus($id, [
                'is_active' => false,
                'deleted_at' => now(),
                'updated_at' => now(),
            ]);

            self::markURLInvite($id);
        }

        self::addFundToSystemCredit($ids[0], $desc);

        app(AuthLogger::class)->info('deleted client' . implode(',', $ids));
    }

    public static function handleExpiry(): void
    {
        $expired = DB::client()->table('user_invites')
            ->join('public.users AS u', 'u.id', 'user_invites.user_id')
            ->where('user_invites.expires_at', '<', Carbon::now())
            ->where('u.deleted_at', null)
            ->get();

        if (!isset($expired)) return;

        foreach ($expired as $ex) {
            $ids = [$ex->user_id];
            self::softDelete($ids, 'Debit fund from expiry for email: ');
        }
    }

    private static function addFundToSystemCredit($id, $desc)
    {
        $invite = DB::client()->table('user_invites')->where('user_id', $id)->first();

        if (!empty($invite)) {
            $balance = json_decode($invite->additional_options)->balance;

            $email = $invite->email;

            $data = [
                'type' => 'debit',
                'amount' => floatval($balance),
                'description' => $desc . $email,
            ];

            AdminCreditService::instance()->store($data, $id);
        }
    }

    private static function changeStatus($id, array $fields)
    {
        app(AuthLogger::class)->info('change status of client id ' . $id . ' to ' . implode(',', $fields));

        return DB::client()->table('users')
            ->whereNull('deleted_at')->where('id', $id)->update($fields);
    }

    private static function markURLInvite($userId)
    {
        return DB::client()->table('user_invites')
            ->where('user_id', $userId)
            ->update([
                'status' => 'revoked',
                'deleted_at' => now()
            ]);
    }

    private static function logClientDeletionEvent(array $ids): void
    {
        $clientEmails = self::getClientEmailsByIds($ids);
        $emailList = implode(', ', $clientEmails);

        event(new AdminActionEvent(
            auth()->user()->id,
            HistoryType::CLIENT_DELETE,
            "Client has been deleted: {$emailList} by " . auth()->user()->email
        ));
    }

    private static function getClientEmailsByIds(array $ids): array
    {
        return DB::client()->table('users')
            ->whereIn('id', $ids)
            ->whereNull('deleted_at')
            ->pluck('email')
            ->toArray();
    }
}
