<?php

namespace App\Modules\DomainRedemption\Requests;

use App\Modules\DomainRedemption\Services\DatabaseQueryService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ShowListRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'orderby' => [
                'string',
                Rule::in([
                    "Domain: Desc",
                    "Domain: Asc",
                    "Date Deleted: Desc",
                    "Date Deleted: Asc",
                ])
            ],
            'user' => ['string, exists:users,email'],
            'domain' => ['string', 'max:255'],
            'email' => ['string', 'max:255'],
            'deletedby' => ['string', 'max:255'],
            'limit' => ['integer', 'min:1', 'max:100'],
            'name' => ['string','max:255']
        ];
    }

    public function show()
    {
        return DatabaseQueryService::instance()->get($this);
    }
}
