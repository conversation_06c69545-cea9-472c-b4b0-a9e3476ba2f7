<?php

namespace App\Modules\Job\Services;

use App\Modules\Job\Constants\ConnectionSource;
use App\Modules\Job\Constants\JobConnection;
use App\Traits\CursorPaginate;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class ShowJobService
{
    use CursorPaginate;

    private static $pageLimit = 10;

    public static function getQueuedJobs($request)
    {
        $queuedJobs =  DB::connection($request->source)->table($request->connection)
            ->orderBy('id', 'desc');

        self::$pageLimit=$request->limit ?? self::$pageLimit;

        $queuedJobs = $queuedJobs->paginate(self::$pageLimit)->withQueryString();

        return CursorPaginate::cursor($queuedJobs, self::paramToURI($request));
    }

    public static function countOnQueue(string $jobName)
    {
        $jobConfig = JobConnection::getByTable($jobName);

        return DB::connection($jobConfig['connection'])->table($jobConfig['table'])->count();
    }

    public static function countOnJobQueue(string $jobName)
    {
        $jobConfig = JobConnection::getByTable($jobName);

        return DB::connection($jobConfig['connection'])->table($jobName)->count();
    }

    public static function countOnFailed(string $jobName)
    {
        $jobConfig = JobConnection::getByTable($jobName);

        return DB::connection($jobConfig['connection'])
            ->table('failed_jobs')->where('connection', $jobName)->count();
    }

    public static function stats(string $jobTable, string $date)
    {
        $stats = DB::connection(ConnectionSource::CLIENT)->table('job_trackers')->where(['connection' => $jobTable, 'date' => $date])->groupBy('date')
            ->select('date', DB::raw('SUM(dispatch) as dispatch'), DB::raw('SUM(success) as success'), DB::raw('SUM(failed) as failed'))->first();

        if (is_null($stats)) {
            return [
                'date' => Carbon::createFromDate($date)->toDateString(),
            ];
        }

        return $stats;
    }

    public static function getData($source = ConnectionSource::CLIENT)
    {
        $jobs = JobConnection::getJobs($source);
        $jobTables = array_column($jobs, 'table');
        $jobStats = self::getJobStats($jobTables);

        return self::getJobStatsTotal($jobTables, $jobStats);
    }

    private static function getJobStats(array $jobTables)
    {
        return DB::connection(ConnectionSource::CLIENT)->table('job_trackers')
            ->whereIn('connection', $jobTables)
            ->select(
                'connection',
                DB::raw('SUM(dispatch) as dispatch'),
                DB::raw('SUM(success) as success'),
                DB::raw('SUM(failed) as failed')
            )
            ->groupBy('connection')->get()->keyBy('connection')->all();
    }

    private static function getJobStatsTotal(array $jobTables, array $jobStats): array
    {
        $allStats = [];
        $totalStats = ['dispatch' => 0, 'success' => 0, 'failed' => 0];

        foreach ($jobTables as $table) {
            $stat = $jobStats[$table] ?? (object) ['connection' => $table, 'dispatch' => 0, 'success' => 0, 'failed' => 0];

            $allStats[] = $stat;
            $totalStats['dispatch'] += $stat->dispatch;
            $totalStats['success'] += $stat->success;
            $totalStats['failed'] += $stat->failed;
        }

        return ['jobs_stats' => $allStats, 'total_stats' => $totalStats];
    }

    private static function paramToURI($request)
    {
        $param = [];

        if ($request->has('connection')) {
            $param[] = 'connection=' . $request->connection;
        }

        if ($request->has('source')) {
            $param[] = 'source=' . $request->source;
        }

        if ($request->has('date')) {
            $param[] = 'date=' . $request->date;
        }

        if ($request->has('tab')) {
            $param[] = 'tab=' . $request->tab;
        }

        return $param;
    }
}
