<?php

namespace App\Modules\MarketPlace\Controllers;

use Inertia\Inertia;

use App\Http\Controllers\Controller;
use App\Modules\MarketPlace\Requests\ManualTransferRequest;
use App\Modules\MarketPlace\Requests\MarketPlaceManualTransferRequest;
use App\Modules\MarketPlace\Services\MarketManualTransferService;
use Illuminate\Support\Facades\DB;

class MarketManualTransferController extends Controller
{
    public function index(MarketPlaceManualTransferRequest $request)
    {
        return Inertia::render('MarketPlace/ManualTransfer', ['data' => MarketManualTransferService::instance()->getData($request)]);
    }

    public function store(ManualTransferRequest $request) : void
    {
        $request->storeAuth();
    }
}
