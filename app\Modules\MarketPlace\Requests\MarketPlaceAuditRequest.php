<?php

namespace App\Modules\MarketPlace\Requests;


use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class MarketPlaceAuditRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'orderby' => ['nullable',Rule::in([
                'Gross: Asc',
                'Gross: Desc'
            ])],
            'domain' => ['nullable','string'],
            'status' => ['nullable',Rule::in([
                'Pending Verification',
                'Filed'
            ])],
            'search' => ['nullable','string']
        ];
    }
}
