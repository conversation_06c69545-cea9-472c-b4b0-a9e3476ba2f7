<?php

namespace App\Modules\MarketPlace\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class MarketPlaceCommissionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'orderby' => ['nullable',Rule::in([
                "User: Asc",
                "User: Desc",
                "Price: Asc",
                "Price: Desc",
                "Date: Asc",
                "Date: Desc",
            ])],
            'domain' => ['string','nullable'],
            'status' => ['nullable',Rule::in([
                "Waiting",
                "Counter Offer",
                "Offer Accepted",
                "Offer Rejected",
                "Offer Closed",
                "Paid",
                "Paid Hold Pending",
                "Paid Order Pending",
                "Paid Transfer Pending",
                "Paid Transfer Requested",
                "Paid Transfer Completed",
                "User Counter",
            ])],
            'audited' => ['string','nullable',Rule::in(['True','False'])],
            'search' => ['string','nullable'],
            'page' => 'nullable|integer|min:1',
            'count' => 'nullable|integer|min:10'
        ];
    }
}
