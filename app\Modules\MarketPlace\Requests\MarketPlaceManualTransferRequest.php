<?php

namespace App\Modules\MarketPlace\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class MarketPlaceManualTransferRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'order by' => ['nullable',Rule::in([
                "Order ID: Asc",
                "Order ID: Desc",
                "Last Updated: Asc",
                "Last Updated: Desc",
            ])],
            'domain' => ['nullable','string'],
            'status' => ['nullable', Rule::in([
                "Submitted",
                "Action Required",
            ])],
            'search' => ['nullable','string']
        ];
    }
}
