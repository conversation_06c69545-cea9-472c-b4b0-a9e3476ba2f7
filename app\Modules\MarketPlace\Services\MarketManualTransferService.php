<?php

namespace App\Modules\MarketPlace\Services;

use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class MarketManualTransferService{

    public Builder $builder;

    public static function instance(){
        $marketManualTransferService = new self;

        return $marketManualTransferService;
    }

    public function getData($request){
        $this->builder = DB::table('public.market_place_domains AS  mpd')
                            ->select('u.first_name', 'u.last_name', 'mpd.order_id', 'd.name as domain', 'mpd.updated_at', 'mpd.epp_error')
                            ->join('public.registered_domains as rd', 'mpd.registered_domain_id', '=', 'rd.id')
                            ->join('public.domains as d', 'rd.domain_id', '=', 'd.id')
                            ->join('public.users as u', 'mpd.user_id', '=', 'u.id')
                            ->where('mpd.epp_error', '!=', 'null');

        $this->builder->when($request->filled('orderby'),function (Builder $builder) use ($request) {
            
            $orderby = explode(':',$request->input('orderby'));
            $orderby[1] = trim($orderby[1]);

            switch($orderby[0]) {
                case 'Order ID':
                    $builder->orderBy('mpd.order_id',$orderby[1]);
                    break;
                case'Last Updated':
                    $builder->orderBy('mpd.updated_at',$orderby[1]);
                    break;
                default:
                    $builder->orderBy('mpd.order_id','desc');
                    break;
            }
        });

        $this->builder->when($request->filled('search'),function (Builder $builder) use ($request) {
            $builder->where('d.name','ilike',$request->input('search')."%");
        });

        $this->builder->when($request->filled('domain'),function(Builder $builder) use ($request){
            $builder->where('d.name','ilike',$request->input('domain')."%");
        });

        $this->builder->when($request->filled('status'),function(Builder $builder) use ($request) {
            $status = strtolower($request->input('status'));
            $status = str_replace(' ','_',$status);

            $builder->where('mpd.status',$request->input('status'));
        });

        return $this->builder->get();

    }
}