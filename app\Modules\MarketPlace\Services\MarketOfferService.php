<?php

namespace App\Modules\MarketPlace\Services;

use App\Models\AfternicOfferHistory;
use App\Models\AfternicOffers;
use App\Modules\MarketPlace\Mail\MarketOfferMail;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class MarketOfferService
{

    public Builder $builder;

    private $perPage = 10;

    public static function instance()
    {
        $marketOfferService = new self;

        return $marketOfferService;
    }

    public function getOffers($request)
    {
        $this->builder = DB::table('public.afternic_offers AS PA')
            ->select('PA.*', 'PU.first_name', 'PU.last_name')
            ->join('public.users AS PU', 'PU.id', 'PA.user_id');

        $this->builder->when($request->filled('orderby'),function(Builder $builder) use ($request){
            $orderby = explode(':',$request->input('orderby'));
            $orderby[1] = trim($orderby[1]);

            switch($orderby[0]) {
                case 'User':
                    $builder->orderby('PU.first_name',$orderby[1]);
                    break;
                case 'Initial Offer':
                    $builder->orderby('PA.offer_price',$orderby[1]);
                    break;
                case 'Last Updated':
                    $builder->orderby('PA.updated_at',$orderby[1]);
                    break;
                case 'Buy Now Price':
                    $builder->orderby('PA.counter_offer_price',$orderby[1]);
                    break;
                default:
                    $builder->orderby('PA.id','desc');
                    break;    
            };
        });

        $this->builder->when($request->filled('search'),function(Builder $builder) use ($request) {
            $builder->where('PA.domain_name','ilike',$request->input('search')."%");
        });

        $this->builder->when($request->filled('domain'),function(Builder $builder) use ($request) {
            $builder->where('PA.domain_name','ilike',$request->input('domain')."%");
        });

        $this->builder->when($request->filled('status'),function(Builder $builder) use ($request){
            $status = strtolower($request->input('status'));
            $status = str_replace(' ','_',$status);

            $builder->where('PA.offer_status',$status);
        });

        return $this->builder->paginate($request->count ? $request->count : $this->perPage, ['*'], 'page', $request->page ? $request->page : 1);
    }
}
