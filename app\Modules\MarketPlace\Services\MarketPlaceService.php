<?php

namespace App\Modules\MarketPlace\Services;

use App\Modules\MarketPlace\Constants\MarketConstants;
use App\Modules\MarketPlace\Mail\AuditMail;
use Carbon\Carbon;
use Illuminate\Database\Query\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;

class MarketPlaceService
{

    public Builder $builder;

    private $per_page = 10;

    public static function instance()
    {
        $marketplaceservice = new self;

        return $marketplaceservice;
    }

    public function getDomains($request) 
    {
        $this->builder = DB::table('public.market_place_domains AS mpd')
        ->join('public.registered_domains AS rd', 'mpd.registered_domain_id', '=', 'rd.id')
        ->join('public.domains AS d', 'rd.domain_id', '=', 'd.id')
        ->join('public.users AS u', 'mpd.user_id', '=', 'u.id')
        ->select('d.id AS domain_id', 'rd.id AS reg_domain_id', 'mpd.id AS market_domain_id', 'u.first_name', 'u.last_name', 'mpd.status', 'd.name AS domain', 'mpd.total_amount as price');
        
        $this->applyFilter($request);

        return $this->builder->paginate($request->count ? $request->count : $this->per_page, ['*'], 'page', $request->page ? $request->page : 1 );
    }

    public function applyFilter($request){
        $this->applyDomainSearch($request);
        $this->applyDomainFilter($request);
        $this->applyOrderByFilter($request);
        $this->applyStatusFilter($request);
    }

    private function applyDomainSearch($request){
        $this->builder->when($request->filled('search'),function(Builder $builder) use ($request) {
            $builder->where('d.name','ilike',$request->input('search')."%");
        });
    }

    private function applyDomainFilter($request){
        $this->builder->when($request->has('domain'),function(Builder $builder) use ($request) {
            $builder->where('d.name','ilike',$request->input('domain')."%");
        });
    }

    private function applyOrderByFilter($request) {
        $this->builder->when($request->has('orderby'),function(Builder $builder) use ($request) {
            $orderby = explode(':',$request->input('orderby'));
            switch ($orderby[0]) {
                case 'User':
                    $builder->orderBy('u.first_name',trim($orderby[1]));
                    break;
                case 'Price':
                    $builder->orderBy('price',trim($orderby[1]));
                    break;
                case 'Created By':
                    $builder->orderBy('u.first_name',trim($orderby[1]));
                    break;
                default:
                    $builder->orderBy('domain_id','desc');
                    break;
            }
        });
    }

    private function applyStatusFilter($request) {
        $this->builder->when($request->has('status'),function(Builder $builder) use ($request){
            
            $status = ($request->input('status') == 'Canceled') ? 'Cancelled' : $request->input('status');

            $builder->where('mpd.status',$status);
        });
    }

    public function setDomainStatus($id, $status) : void
    {
        DB::table('public.market_place_domains')
        ->where('id', $id)
        ->update(['status' => $status]);
    }
}