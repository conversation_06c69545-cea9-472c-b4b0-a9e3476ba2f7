<?php

namespace App\Modules\Notification\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Notification\Requests\UpdateNotificationReadRequest;
use App\Modules\Notification\Services\NotificationService;
use Inertia\Inertia;

class NotificationController extends Controller
{
    public function index()
    {
        return Inertia::render('Notification/Index', NotificationService::getAll());
    }

    public function getUnreadCount(NotificationService $notificationService)
    {
        $totalUnreadNotif = $notificationService->getUnreadCount();

        return response($totalUnreadNotif, 200);
    }

    public function getDropdownData()
    {
        //return response(NotificationService::getAll(), 200);
        return response(NotificationService::getAll(), 200);
        
    }

    public function update(UpdateNotificationReadRequest $request)
    {
        $url = trim($request->read(), " '");
        [$routeName, $queryString] = explode('?', $url . '?', 2);

        $queryParams = [];
        if (!empty($queryString)) parse_str($queryString, $queryParams);

        return redirect()->route($routeName, $queryParams);
    }

    // public function store(CreateCategoryRequest $request)
    // {
    //     $request->store();
    //     return redirect()->route('category');
    // }

    // // public function show()
    // // {
    // //     //
    // // }

    // public function edit(Request $request, CategoryService $service)
    // {
    //     $data = $service->getCategories($request->query('ids'));
    //     return Inertia::render("Category/Edit", ['categories' => $data]);
    // }

    // public function destroy(Request $request, CategoryService $service)
    // {
    //     $service->sofDelete($request->query('ids'));
    //     return redirect()->route('category');
    // }
}
