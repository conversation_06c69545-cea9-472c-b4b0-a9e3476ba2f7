<?php

namespace App\Modules\Notification\Services;

use App\Models\ScheduleNotification;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use App\Modules\Notification\Requests\UpdateNotificationStatus;
use App\Modules\Notification\Requests\DeleteNotificationRequest;
use App\Modules\Notification\Constants\NotificationStatus;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Response;

class NotificationSchedulerService
{
    private static $pageLimit = 20;

    public function processNotificationList(Request $request): array
    {
        $notificationsData = $this->getNotifications($request);
        $configData = $this->getConfigurationData();
        $filterData = $this->getFilterData($request);
        
        return array_merge($notificationsData, $configData, $filterData);
    }

    private function getNotifications(Request $request): array
    {
        $query = $this->initializeQuery();
        $this->applyFilters($query, $request);
        $this->applySorting($query, $request);
        
        $notifications = $this->getPaginatedResults($query, $request);
        $this->attachUserInformation($notifications);

        return [
            'notifications' => $notifications
        ];
    }

    private function getConfigurationData(): array
    {
        return [
            'statuses' => NotificationStatus::toArray(),
            'types' => $this->getNotificationTypes(),
            'schedule_types' => $this->getScheduleTypes(),
            'limits' => $this->getPaginationLimits()
        ];
    }

    private function getFilterData(Request $request): array
    {
        return [
            'filters' => $this->getFilterParameters($request)
        ];
    }

    public function processStatusUpdate(UpdateNotificationStatus $request): RedirectResponse
    {
        $ids = $this->validateNotificationIds($request->validated('ids'));
        $this->updateNotificationStatuses($ids, $request->validated('status'));
        
        return $this->getSuccessResponse('Notifications updated successfully');
    }

    public function processDelete(DeleteNotificationRequest $request): RedirectResponse
    {
        $ids = $this->validateNotificationIds($request->validated('ids'));
        $this->deleteNotifications($ids);
        
        return $this->getSuccessResponse('Notifications deleted successfully');
    }

    public function processUsersList(int $id, Request $request): array
    {
        $notification = $this->findNotification($id);
        $users = $this->getPaginatedUsersForNotification($notification, $request->get('limit'));
        
        return [
            'notification' => [
                'id' => $notification->id,
                'title' => $notification->title,
                'status' => $notification->status,
                'type' => $notification->type,
                'schedule_type' => $notification->schedule_type,
                'min_registration_period' => $notification->min_registration_period,
                'max_registration_period' => $notification->max_registration_period,
                'users' => [
                    'count' => $users->total(),
                    'list' => $users->items(),
                    'current_page' => $users->currentPage(),
                    'last_page' => $users->lastPage(),
                    'per_page' => $users->perPage(),
                ]
            ]
        ];
    }

    // Helper methods
    private function initializeQuery()
    {
        return DB::client()->table('schedule_notifications')
            ->selectRaw('
                schedule_notifications.created_at,
                schedule_notifications.start_date,
                MIN(schedule_notifications.id) as id,
                MIN(schedule_notifications.title) as title,
                MIN(schedule_notifications.status) as status,
                MIN(schedule_notifications.type) as type,
                MIN(schedule_notifications.schedule_type) as schedule_type,
                MIN(schedule_notifications.user_id) as user_id
            ')
            ->groupByRaw('schedule_notifications.created_at, schedule_notifications.start_date');
    }

    private function applyFilters($query, Request $request): void
    {
        if ($request->filled('status') && $request->status !== 'null') {
            $query->where('status', strtolower($request->status));
        }
        if ($request->filled('type') && $request->type !== 'null') {
            $query->where('type', $request->type);
        }
        if ($request->filled('schedule_type') && $request->schedule_type !== 'null') {
            $schedule_type = strtolower($request->schedule_type);
            switch ($schedule_type) {
                case 'one time':
                    $query->where('schedule_type', 'one-time');
                    break;
                default:
                    $query->where('schedule_type', $schedule_type);
                    break;
            }
            
        }
    }

    private function applySorting($query, Request $request): void
    {
        // $direction = $request->filled('orderby') ? 
        //     trim(explode(':', $request->orderby)[1]) ?? 'desc' : 
        //     'desc';

        if ($request->filled('orderby')) {

            $orderby = explode(':',$request->input('orderby'));
            $orderby[1] = trim($orderby[1]);

            if (in_array($orderby[1],['Asc','Desc'])){
                switch ($orderby[0]) {
                    case 'Next Date':
                        $query->orderBy('schedule_notifications.start_date', $orderby[1]);
                        return;
                    default:
                        # code...
                        break;
                }
            }
        }
        $query->orderBy('created_at', 'desc');
    }

    private function getPaginatedResults($query, Request $request)
    {
        return $query->paginate($request->get('limit', self::$pageLimit))
            ->appends($request->all());
    }

    private function attachUserInformation($notifications): void
    {
        foreach ($notifications as $notification) {
            $notification->users = $this->getUsersForNotification($notification);
        }
    }

    private function validateNotificationIds(array $ids): array
    {
        return array_filter($ids, function($id) {
            return DB::client()->table('schedule_notifications')->where('id', $id)->exists();
        });
    }

    private function updateNotificationStatuses(array $ids, string $status): void
    {
        foreach ($ids as $id) {
            $notification = DB::client()->table('schedule_notifications')->find($id);
            if ($notification) {
                DB::client()->table('schedule_notifications')
                    ->where('created_at', $notification->created_at)
                    ->where('start_date', $notification->start_date)
                    ->update(['status' => $status]);
            }
        }
    }

    private function deleteNotifications(array $ids): void
    {
        foreach ($ids as $id) {
            $notification = DB::client()->table('schedule_notifications')->find($id);
            if ($notification) {
                DB::client()->table('schedule_notifications')
                    ->where('created_at', $notification->created_at)
                    ->where('start_date', $notification->start_date)
                    ->delete();
            }
        }
    }

    private function findNotification(int $id)
    {
        return DB::client()->table('schedule_notifications')
            ->where('id', $id)
            ->first();
    }

    private function getSuccessResponse(string $message): RedirectResponse
    {
        return redirect()->back()->with('success', $message);
    }

    private function getPaginatedUsersForNotification($notification, ?int $limit = null): object
    {
        $notificationUsers = DB::client()->table('schedule_notifications')
            ->where('created_at', $notification->created_at)
            ->where('start_date', $notification->start_date)
            ->select('user_id', 'read_at')
            ->get();

        $userIds = $notificationUsers->pluck('user_id')->toArray();
        $users = DB::client()->table('users')
            ->select('id', 'email')
            ->whereIn('id', $userIds)
            ->paginate($limit ?? '10');

        // merge and pagination
        $users->through(function($user) use ($notificationUsers) {
            $notificationUser = $notificationUsers->firstWhere('user_id', $user->id);
            $user->read_at = $notificationUser ? $notificationUser->read_at : null;
            return $user;
        });

        return $users;
    }

    private function getUsersForNotification(object $notification): array
    {
        $users = $this->getPaginatedUsersForNotification($notification);
        
        return [
            'count' => $users->total(),
            'list' => $users->items(),
            'is_all_users' => false
        ];
    }

    private function getFilterParameters(Request $request): array
    {
        return [
            'status' => $request->status !== 'null' ? $request->status : '',
            'type' => $request->type !== 'null' ? $request->type : '',
            'schedule_type' => $request->schedule_type !== 'null' ? $request->schedule_type : '',
            'orderby' => $request->orderby ?? '',
            'limit' => $request->get('limit', self::$pageLimit)
        ];
    }

    private function getNotificationTypes(): array
    {
        return ['Important', 'Normal'];
    }

    private function getScheduleTypes(): array
    {
        return ['one-time', 'weekly', 'monthly', 'yearly'];
    }

    private function getPaginationLimits(): array
    {
        return [20, 30, 50, 100];
    }
} 