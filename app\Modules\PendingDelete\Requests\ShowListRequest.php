<?php

namespace App\Modules\PendingDelete\Requests;

use App\Modules\PendingDelete\Constants\StatusTypes;
use App\Modules\PendingDelete\Services\DatabaseQueryService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ShowListRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'statusType' => ['string', Rule::in(StatusTypes::TYPES)],
            'orderby' => ['string', Rule::in([
                'Date Scraped: Desc',
                'Date Scraped: Asc',
                'Domain: Desc',
                'Domain: Asc',
                'Days Expired: Desc',
                'Days Expired: Asc',
                'Date Deleted: Desc',
                'Date Deleted: Asc'
            ])],
            'user' => ['string'],
            'domain' => ['string'],
            'email' => ['string'],
            'deletedBy' => ['string']
        ];
    }

    public function show()
    {
        return DatabaseQueryService::instance()->get($this);
    }
}
