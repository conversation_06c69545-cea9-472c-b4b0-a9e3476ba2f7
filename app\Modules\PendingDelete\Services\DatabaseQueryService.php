<?php

namespace App\Modules\PendingDelete\Services;

use App\Modules\PendingDelete\Constants\StatusTypes;
use App\Modules\PendingDelete\Requests\ShowListRequest;
use App\Traits\CursorPaginate;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class DatabaseQueryService
{
    use CursorPaginate;

    private static $pageLimit = 20;

    public static function instance()
    {
        $DatabaseQueryService = new self;

        return $DatabaseQueryService;
    }

    public function get(ShowListRequest $request)
    {
        $pageLimit = $request->input('limit', self::$pageLimit);
        $builder = self::baseQuery();
        self::whenHasUser($builder, $request);
        self::whenHasDomain($builder, $request);
        self::whenHasEmail($builder, $request);
        self::whenHasDeletedBy($builder, $request);
        self::whenHasStatusType($builder, $request);
        self::whenHas<PERSON><PERSON>rby($builder, $request);
        $builder = $builder->paginate($pageLimit)->withQueryString();
        return [
            ...CursorPaginate::cursor($builder, self::paramToURI($request)),
            "search" => $request->search ?? ""
        ];
    }

    public function domainSummary(array $ids): array
    {
        return $this->pendingDomains($ids)
            ->join('registered_domains', 'registered_domains.id', '=', 'pending_domain_deletions.registered_domain_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->select('pending_domain_deletions.id as id', 'domains.name', )
            ->get()->all();
    }

    public function getDomainRecords(Builder $pendingDomains): array
    {
        return $pendingDomains->join('registered_domains', 'registered_domains.id', '=', 'pending_domain_deletions.registered_domain_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->join('users', 'users.id', '=', 'user_contacts.user_id')
            ->select(
                'pending_domain_deletions.id as id',
                'pending_domain_deletions.created_at',
                'pending_domain_deletions.updated_at',
                'domains.id as domain_id',
                'domains.name',
                'registered_domains.id as registered_domain_id',
                'users.email as user_email',
                'users.id as user_id',
                'users.first_name',
                'users.last_name'
            )->get()->toArray();
    }

    public function pendingDomains(array $ids): Builder
    {
        return DB::client()->table('pending_domain_deletions')
            ->whereIn('pending_domain_deletions.id', $ids);
    }

    // PRIVATE Functions

    private function baseQuery(): Builder
    {
        return DB::client()->table('pending_domain_deletions')
            ->join('registered_domains', 'registered_domains.id', '=', 'pending_domain_deletions.registered_domain_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->join('users', 'users.id', '=', 'user_contacts.user_id')
            ->select(self::getSelectFields());
    }

    private function getSelectFields(): array
    {
        return [
            'pending_domain_deletions.id as delete_id',
            'pending_domain_deletions.deleted_by',
            'pending_domain_deletions.deleted_at as domain_deleted_at',
            'pending_domain_deletions.created_at as scraped_at',
            'domains.*',
            'registered_domains.id as registered_domain_id',
            'users.email as user_email',
            'users.first_name',
            'users.last_name'
        ];
    }

    private function whenHasStatusType(Builder &$builder, ShowListRequest $request): void
    {
        if (!$request->has('statusType')) {
            $builder->whereNull('pending_domain_deletions.deleted_at');
            return;
        }

        match ($request->statusType) {
            StatusTypes::PENDING => $builder->whereNull('pending_domain_deletions.deleted_at'),
            StatusTypes::DELETED => $builder->whereNotNull('pending_domain_deletions.deleted_at'),
            default => $builder->whereNull('pending_domain_deletions.deleted_at')
        };
    }

    private function whenHasOrderby(Builder &$builder, ShowListRequest $request): void
    {
        $builder->when($request->has('orderby'), function (Builder $query) use ($request) {
            $orderby = explode(':', $request->orderby);

            if (count($orderby) == 2 && in_array($orderby[1], [' Asc', ' Desc'])) {
                switch ($orderby[0]) {
                    case 'Date Scraped':
                        $query->orderBy('pending_domain_deletions.created_at', trim($orderby[1]));
                        break;
                    case 'Days Expired':
                        $query->orderBy('expiry', trim($orderby[1]));
                        break;
                    case 'Domain':
                        $query->orderBy('name', trim($orderby[1]));
                        break;
                    case 'Date Deleted':
                        $query->orderBy('pending_domain_deletions.deleted_at', trim($orderby[1]));
                        break;
                    default:
                        $query->orderBy('delete_id', 'desc');
                }
            } else {
                $query->orderBy('delete_id', 'desc');
            }
        })
            ->when(!$request->has('orderby'), function (Builder $query) {
                $query->orderBy('delete_id', 'desc');
            });
    }

    private static function whenHasUser(&$builder, $request)
    {
        $builder->when($request->has('user'), function (Builder $query) use ($request) {
            $user = $request->user;
            $query->where(DB::raw("first_name || ' ' || last_name"), 'ILIKE', $user . '%');
        });
    }

    private static function whenHasDomain(&$builder, $request)
    {
        $builder->when(($request->has('domain') || $request->has('search')), function (Builder $query) use ($request) {
            $domain = $request->domain ?? $request->search;
            $query->where('name', 'ilike', $domain . '%');
        });
    }

    private static function whenHasEmail(&$builder, $request)
    {
        $builder->when($request->has('email'), function (Builder $query) use ($request) {
            $email = $request->email;
            $query->where('users.email', 'ilike', $email . '%');
        });
    }

    private static function whenHasDeletedBy(&$builder, $request)
    {
        $builder->when($request->has('deletedBy'), function (Builder $query) use ($request) {
            $deletedBy = $request->deletedBy;
            $query->where('deleted_by', 'ilike', $deletedBy . '%');
        });
    }

    private function paramToURI(ShowListRequest $request): array
    {
        $param = [];

        if ($request->has('statusType')) {
            $param[] = 'statusType=' . $request->statusType;
        }

        if ($request->has('orderby')) {
            $param[] = 'orderby=' . $request->orderby;
        }

        return $param;
    }
}
