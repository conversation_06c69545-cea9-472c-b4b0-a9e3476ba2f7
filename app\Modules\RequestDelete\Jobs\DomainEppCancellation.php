<?php

namespace App\Modules\RequestDelete\Jobs;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\RequestDelete\Services\DomainEppCancellationJobService;
use App\Modules\RequestDelete\Services\DomainDeleteService;
use App\Util\Constant\QueueConnection;
use App\Util\Constant\QueueTypes;
use App\Util\Helper\DomainParser;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Exception;
use Throwable;

class DomainEppCancellation implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, UserLoggerTrait;

    private $params;

    /**
     * Maximum number of attempts before the job fails
     */
    public $tries = 3;

    /**
     * if process takes longer than indicated  timeout ie. --timeout=30
     * set the job to failed job
     */
    public $failOnTimeout = true;

    /**
     * Create a new job instance.
     */
    public function __construct(
        string $domainId,
        string $domainName,
        string $userId,
        string $userEmail,
        string $reason,
        string $createdDate,
        string $supportNote = null,
        int $adminId = null,
        string $adminName = null,
        string $adminEmail = null,
        int $pendingTransferRetryAttempt = 0
    ) {
        $registry = DomainParser::getRegistryName($domainName);

        $this->params = [
            'domainId' => $domainId,
            'domainName' => $domainName,
            'userId' => $userId,
            'userEmail' => $userEmail,
            'reason' => $reason,
            'createdDate' => $createdDate,
            'supportNote' => $supportNote,
            'adminId' => $adminId,
            'adminName' => $adminName,
            'adminEmail' => $adminEmail,
            'pendingTransferRetryAttempt' => $pendingTransferRetryAttempt
        ];

        $this->onConnection(QueueConnection::DOMAIN_CANCELLATION);
        $this->onQueue(QueueTypes::DOMAIN_CANCELLATION[$registry]);
    }

    public $uniqueFor = 3600;

    public function uniqueId(): string
    {
        return $this->params['domainId'] . '_' . $this->params['domainName'] . '_retry_' . $this->params['pendingTransferRetryAttempt'];
    }

    public function handle(): void
    {
        try {
            app(AuthLogger::class)->info("Starting domain deletion job for domain: {$this->params['domainName']} (ID: {$this->params['domainId']}) - Attempt {$this->attempts()}/{$this->tries}");
            DomainEppCancellationJobService::instance()->eppDelete($this->params);
            app(AuthLogger::class)->info("Successfully completed domain deletion job for domain: {$this->params['domainName']}");
        } catch (Exception $e) {
            app(AuthLogger::class)->error("Domain deletion job failed for domain: {$this->params['domainName']} (ID: {$this->params['domainId']}) - Attempt {$this->attempts()}/{$this->tries}. Error: " . $e->getMessage());

            // If this is the final attempt, log additional failure information
            if ($this->attempts() >= $this->tries) {
                app(AuthLogger::class)->error("Domain deletion job permanently failed for domain: {$this->params['domainName']} after {$this->tries} attempts. Job will be moved to failed jobs table.");
            }

            throw $e;
        }
    }

    public function backoff(): array
    {
        return [30, 60]; // Wait 30s, then 60s between retries
    }

    public function failed(?Throwable $exception): void
    {
        app(AuthLogger::class)->error("Domain deletion job permanently failed for domain: {$this->params['domainName']} (ID: {$this->params['domainId']}) after {$this->tries} attempts. Final error: " . $exception->getMessage());

        try {
            $request = (object) [
                'domainId' => $this->params['domainId'],
                'support_note' => 'Domain deletion request canceled due to repeated EPP failures: ' . $this->params['domainName']
            ];

            DomainDeleteService::instance()->cancelDeleteRequest($request);
            $this->dispatchFailedDomainHistory();
        } catch (Exception $e) {
            app(AuthLogger::class)->error("Failed to handle permanent job failure for domain {$this->params['domainName']}: " . $e->getMessage());
        }
    }

    private function dispatchFailedDomainHistory(): void
    {
        $adminName = $this->params['adminName'] ?? 'System';
        $adminEmail = $this->params['adminEmail'] ?? '<EMAIL>';

        $message = 'Domain "' . $this->params['domainName'] . '" deletion failed by ' . $adminName . ' (' . $adminEmail . ')';

        DB::client()->table('domain_transaction_histories')->insert([
            'domain_id' => $this->params['domainId'],
            'type'      => 'DOMAIN_DELETED',
            'user_id'   => $this->params['userId'],
            'status'    => 'failed',
            'message'   => $message,
            'payload'   => json_encode($this->params),
            'created_at'=> now(),
            'updated_at'=> now(),
        ]);
    }
}
