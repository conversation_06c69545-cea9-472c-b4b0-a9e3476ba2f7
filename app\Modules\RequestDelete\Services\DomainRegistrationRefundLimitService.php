<?php

namespace App\Modules\RequestDelete\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use App\Modules\CustomLogger\Services\AuthLogger;

class DomainRegistrationRefundLimitService
{
    public static function instance(): self
    {
        return new self();
    }

    public function incrementCounter(int $registeredDomainId): void
    {
        if (!$this->isNewlyRegisteredDomain($registeredDomainId)) {
            return;
        }

        $limitRecord = $this->getGlobalLimitRecord();

        DB::client()->table('domain_registration_refund_limit')
            ->where('id', $limitRecord->id)
            ->increment('times_triggered');

        $newCount = $limitRecord->times_triggered + 1;
        if ($newCount > $limitRecord->limit) {
            app(AuthLogger::class)->info("Domain refund limit EXCEEDED. Count: {$newCount}, Limit: {$limitRecord->limit}");
        } else {
            app(AuthLogger::class)->info("Domain refund limit counter incremented. Count: {$newCount}, Limit: {$limitRecord->limit}");
        }
    }

    public function decrementCounter(int $registeredDomainId): void
    {
        if (!$this->isNewlyRegisteredDomain($registeredDomainId)) {
            return;
        }

        $limitRecord = $this->getGlobalLimitRecord();

        if ($limitRecord->times_triggered > 0) {
            DB::client()->table('domain_registration_refund_limit')
                ->where('id', $limitRecord->id)
                ->decrement('times_triggered');

            app(AuthLogger::class)->info("Domain refund limit counter decremented. New count: " . ($limitRecord->times_triggered - 1));
        }
    }

    private function isNewlyRegisteredDomain(int $registeredDomainId): bool
    {
        $domain = DB::client()->table('registered_domains')
            ->join('domains', 'registered_domains.domain_id', '=', 'domains.id')
            ->where('registered_domains.id', $registeredDomainId)
            ->select('domains.created_at')
            ->first();

        if (!$domain) {
            return false;
        }

        $domainCreatedAt = Carbon::parse($domain->created_at);
        $daysSinceCreation = $domainCreatedAt->diffInDays(Carbon::now());

        return $daysSinceCreation <= 5;
    }

    private function getGlobalLimitRecord(): object
    {
        $record = DB::client()->table('domain_registration_refund_limit')
            ->first();

        if (!$record) {
            throw new \Exception('Global domain registration refund limit record not found. Please run migration.');
        }

        return $record;
    }

    public function calculateAndUpdateMonthlyLimits(): void
    {
        $previousMonth = Carbon::now()->subMonth();
        $startOfMonth = $previousMonth->startOfMonth();
        $endOfMonth = $previousMonth->endOfMonth();

        $totalRegistrations = DB::client()->table('domains')
            ->whereBetween('created_at', [$startOfMonth, $endOfMonth])
            ->count();

        // 10% of total registrations
        $calculatedLimit = (int) ceil($totalRegistrations * 0.10);
        
        // calc new limit
        $newLimit = max($calculatedLimit, 50);


        $updatedCount = DB::client()->table('domain_registration_refund_limit')
            ->update([
                'limit' => $newLimit,
                'updated_at' => now(),
            ]);

        app(AuthLogger::class)->info("Monthly limit calculation completed. Total registrations: {$totalRegistrations}, New limit: {$newLimit}, Records updated: {$updatedCount}");
    }

    public function resetMonthlyCounters(): void
    {
        $updatedCount = DB::client()->table('domain_registration_refund_limit')
            ->update([
                'times_triggered' => 0,
                'updated_at' => now(),
            ]);

        app(AuthLogger::class)->info("Monthly counter reset completed. Records reset: {$updatedCount}");
    }

}
