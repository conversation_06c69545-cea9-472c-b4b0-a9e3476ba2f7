<?php

namespace App\Modules\Setting\UserTransaction\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Setting\UserTransaction\Requests\TransactionDetailsRequest;
use App\Modules\Setting\UserTransaction\Services\UserTransaction;
use Inertia\Inertia;

class UserTransactionController extends Controller
{
    public function get()
    {
        $data = UserTransaction::instance()->getTransactions();

        // dd($data);
        return Inertia::render('Client/UserTransaction/Dashboard', [
            'items' => $data,
        ]);
    }

    public function viewCard(TransactionDetailsRequest $request)
    {
        $data = $request->getData();

        return response()->json(['data' => $data], 200);
    }

    public function viewCardTotal(TransactionDetailsRequest $request)
    {
        $data = $request->getTotalData();

        return response()->json(['data' => $data], 200);
    }
}
