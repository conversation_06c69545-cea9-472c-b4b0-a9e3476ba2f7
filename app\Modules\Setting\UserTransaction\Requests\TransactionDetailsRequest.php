<?php

namespace App\Modules\Setting\UserTransaction\Requests;

use App\Modules\Setting\UserTransaction\Services\UserTransaction;
use App\Util\Constant\Transaction;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class TransactionDetailsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'transaction' => ['nullable', 'string', Rule::in(array_keys(Transaction::TYPES))]
        ];
    }

    public function getData()
    {
        return UserTransaction::instance()->getTransactionDetails($this->transaction);
    }

    public function getTotalData()
    {
        return UserTransaction::instance()->getTotalTransactionDetails();
    }
}
