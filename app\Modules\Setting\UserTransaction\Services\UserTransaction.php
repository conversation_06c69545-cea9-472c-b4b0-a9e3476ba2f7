<?php

namespace App\Modules\Setting\UserTransaction\Services;

use Illuminate\Support\Facades\DB;

class UserTransaction
{
    public static function instance(): self
    {
        $userTransaction = new self;

        return $userTransaction;
    }

    public function getTransactions()
    {
        return DB::client()->table('transactions')
            ->select(
                'transactions.id as transaction_id',
                'transactions.name as transaction_name',
                DB::raw('COALESCE(SUM(system_transaction_observers.past_counter), 0) as count'),
                DB::raw('COALESCE(SUM(system_transaction_observers.hits), 0) as hits'),
                DB::raw('COALESCE(SUM(system_transaction_observers.approved), 0) as approved'),
                DB::raw('COALESCE(SUM(system_transaction_observers.rejected), 0) as rejected')
            )
            ->leftJoin('system_transaction_observers', 'transactions.id', '=', 'system_transaction_observers.transaction_id')
            ->groupBy('transactions.id', 'transactions.name')
            ->get();
    }

    public function getTransactionDetails(string $transaction)
    {
        return DB::client()->table('user_transaction_observers')
            ->select(
                'transactions.name as transaction_name',
                'users.id as user_id',
                'users.email',
                DB::raw("(users.first_name || ' ' || users.last_name) as user_name"),
                DB::raw('SUM(user_transaction_observers.past_counter) as count'),
                DB::raw('SUM(user_transaction_observers.hits) as hits'),
                DB::raw('SUM(user_transaction_observers.approved) as approved'),
                DB::raw('SUM(user_transaction_observers.rejected) as rejected')
            )
            ->join('user_transactions', 'user_transactions.id', '=', 'user_transaction_observers.user_transaction_id')
            ->join('transactions', 'transactions.id', '=', 'user_transactions.transaction_id')
            ->join('users', 'users.id', '=', 'user_transactions.user_id')
            ->where('transactions.name', $transaction)
            ->groupBy(
                'transactions.name',
                'users.id',
                'users.email',
                DB::raw("(users.first_name || ' ' || users.last_name)")
            )
            ->get();
    }

    public function getTotalTransactionDetails()
    {
        return DB::client()->table('transactions')
            ->select(
                'transactions.name as transaction_name',
                DB::raw('COALESCE(SUM(system_transaction_observers.past_counter), 0) as count'),
                DB::raw('COALESCE(SUM(system_transaction_observers.hits), 0) as hits'),
                DB::raw('COALESCE(SUM(system_transaction_observers.approved), 0) as approved'),
                DB::raw('COALESCE(SUM(system_transaction_observers.rejected), 0) as rejected')
            )
            ->leftjoin('system_transaction_observers', 'system_transaction_observers.transaction_id', '=', 'transactions.id')
            ->groupBy('transactions.name',)
            ->get();
    }
}
