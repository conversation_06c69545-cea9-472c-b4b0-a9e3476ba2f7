<?php

namespace App\Modules\Transfer\Jobs;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Client\Constants\DomainStatus;
use App\Modules\Transfer\Services\JobTransferService;
use App\Util\Constant\QueueConnection;
use App\Util\Constant\QueueErrorTypes;
use App\Util\Constant\QueueTypes;
use App\Util\Helper\DomainParser;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Throwable;

class SendEppTransferRequestResponse implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, UserLoggerTrait;

    private $params;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 120;

    /**
     * if process takes longer than indicated  timeout ie. --timeout=30
     * set the job to failed job
     */
    public $failOnTimeout = true;

    /**
     * Create a new job instance.
     */
    public function __construct($transferId, $domainName, $domainId, $regDomainId, $userId, $email, $action)
    {
        $registry = DomainParser::getRegistryName($domainName);

        $this->params = [
            'transferId' => $transferId,
            'domainName' => $domainName,
            'domainId' => $domainId,
            'registeredDomainId' => $regDomainId,
            'userId' => $userId,
            'email' => $email,
            'action' => $action,
        ];

        $this->onConnection(QueueConnection::DOMAIN_TRANSFER_APPROVED);
        $this->onQueue(QueueTypes::DOMAIN_TRANSFER_APPROVED[$registry]);
    }

    public $uniqueFor = 3600;

    public function uniqueId(): int
    {
        return $this->params['registeredDomainId'];
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            JobTransferService::instance()->userRequestAction($this->params);
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho('Transfer job failed: ' . $e->getMessage(), $this->params['email']));
            app(AuthLogger::class)->info('Transfer job attempt: ' . $this->attempts() . '/' . $this->tries);

            if (strcmp($e->getMessage(), QueueErrorTypes::RETRY) === 0) {
                throw $e; 
            }

            throw $e;
        }
    }

    /**
     * Define the backoff strategy for retries.
     *
     * @return array
     */
    public function backoff(): array
    {
        return [30, 60, 120]; // Wait 30s, then 60s, then 120s between retries
    }

    /**
     * Handle a job failure.
     */
    public function failed(?Throwable $exception): void
    {
        app(AuthLogger::class)->error($this->fromWho('Transfer job permanently failed after ' . $this->tries . ' attempts: ' . ($exception ? $exception->getMessage() : 'Unknown error'), $this->params['email']));

        // TODO: Send user notification of permanent failure
        // NotificationService::sendTransferFailedNotification($this->params['userId'], $this->params['domainName']);
    }
}
