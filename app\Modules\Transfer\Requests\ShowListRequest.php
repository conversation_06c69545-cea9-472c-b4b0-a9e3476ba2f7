<?php

namespace App\Modules\Transfer\Requests;

use App\Modules\Transfer\Services\TransferService;
use Illuminate\Foundation\Http\FormRequest;

class ShowListRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'orderby' => ['string'],
            'email'   => ['string'],
            'limit'   => ['integer', 'min:1', 'max:100'],
        ];
    }

    public function show()
    {
        return TransferService::instance()->get($this);
    }
}
