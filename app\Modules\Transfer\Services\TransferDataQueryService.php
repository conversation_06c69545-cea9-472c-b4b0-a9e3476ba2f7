<?php

namespace App\Modules\Transfer\Services;

use App\Modules\Transfer\Constants\TransferRequest;
use Illuminate\Support\Facades\DB;
use stdClass;

class TransferDataQueryService
{
    public static function instance()
    {
        $transferDataQueryService = new self;

        return $transferDataQueryService;
    }

    public function getTransferUpdateData(array $ids): array
    {
        return DB::client()->table('transfer_domains')
            ->select('transfer_domains.*', 'domains.id as domain_id', 'domains.name as domain_name', 'user_contacts.user_id')
            ->join('registered_domains', 'registered_domains.id', '=', 'transfer_domains.registered_domain_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->whereIn('transfer_domains.id', $ids)
            ->get()->toArray();
    }

}