<?php

namespace App\Modules\Transfer\Services;

use App\Traits\CursorPaginate;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Query\Builder;
use App\Modules\Epp\Constants\EppDomainStatus;

class TransferService
{
    use CursorPaginate;

    private static $pageLimit = 20;

    public static function instance(): self
    {
        return new self;
    }

    // private function baseQuery()
    // {
    //     return DB::client()->table('transfer_domains')
    //         ->join('registered_domains', 'registered_domains.id', '=', 'transfer_domains.registered_domain_id')
    //         ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
    //         ->join('users', 'users.id', '=', 'user_contacts.user_id')
    //         ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
    //         ->select([
    //             'transfer_domains.id',
    //             'transfer_domains.registered_domain_id',
    //             'transfer_domains.deleted_at',
    //             'transfer_domains.created_at',
    //             'transfer_domains.updated_at',
    //             'domains.name as domain',
    //             'domains.id as domain_id',
    //             'users.email as user_email',
    //             'users.first_name',
    //             'users.last_name',
    //         ])
    //         ->whereNull('transfer_domains.deleted_at');
    // }

    private function baseQuery(): Builder
    {
        return DB::client()->table('transfer_domains')
            ->select('transfer_domains.*', 'domains.id as domain_id', 'domains.name as domain', 'users.email as user_email' , 'user_contacts.user_id', 'registered_domains.status as registered_domain_status')
            ->join('registered_domains', 'registered_domains.id', '=', 'transfer_domains.registered_domain_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->join('users', 'users.id', '=', 'user_contacts.user_id')
            ->where('transfer_domains.status', EppDomainStatus::TRANSFER_PENDING);
    }

    public function get($request): array
    {
        $pageLimit = $request->input('limit', self::$pageLimit);
        $builder = $this->baseQuery();

        $this->whenHasEmail($builder, $request);
        $this->whenHasOrderby($builder, $request);

        $builder = $builder->paginate($pageLimit)->withQueryString();

        return [
            ...CursorPaginate::cursor($builder, $this->paramToURI($request)),
            "search" => $request->search ?? ""
        ];
    }

    private function whenHasEmail($builder, $request)
    {
        if ($request->has('email') && !empty($request->email)) {
            $builder->where('users.email', 'like', '%' . $request->email . '%');
        }
    }

    private function whenHasOrderby($builder, $request)
    {
        if ($request->has('orderby') && !empty($request->orderby)) {
            $orderParts = explode(':', $request->orderby);
            $column = $orderParts[0];
            $direction = $orderParts[1] ?? 'desc';

            switch ($column) {
                case 'domain':
                    $builder->orderBy('domains.name', $direction);
                    break;
                case 'date_created':
                    $builder->orderBy('transfer_domains.created_at', $direction);
                    break;
                default:
                    $builder->orderBy('transfer_domains.created_at', 'desc');
            }
        } else {
            $builder->orderBy('transfer_domains.created_at', 'desc');
        }
    }

    private function paramToURI($request): array
    {
        $params = [];

        if ($request->has('email') && !empty($request->email)) {
            $params[] = 'email=' . urlencode($request->email);
        }

        if ($request->has('orderby') && !empty($request->orderby)) {
            $params[] = 'orderby=' . urlencode($request->orderby);
        }

        if ($request->has('limit') && !empty($request->limit)) {
            $params[] = 'limit=' . urlencode($request->limit);
        }

        return $params;
    }
}
