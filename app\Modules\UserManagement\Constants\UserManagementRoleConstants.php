<?php

namespace App\Modules\UserManagement\Constants;

final class UserManagementRoleConstants
{
    /**
     * @var string
     */
    public const STATUS_ACTIVE   = 'active';

    /**
     * @var string 
     */
    public const STATUS_PENDING  = 'pending';

    /**
     * @var string 
     */
    public const STATUS_DISABLED = 'disabled';

    /**
     * @var array
     */
    public const PREDEFINED_DEFAULTS =
    [
        [
            'name'                      => 'Admin',
            'permissionByCategoryGroup' =>
            [
                'Audit',
                'EPP Registry',
                'Offers',
                //'User Management', 
                'Billing',
                'Client Management',
                'Domain Management',
                'Job Queues',
                'Marketplace',
                'Notification Management',
                'Settings',
                'System Activity Monitor',
                'System Credits',
                'System Emails'
            ],
            'excludedPermissions' =>
            [],
            'additionalPermissions' =>
            [],
        ],
        [
            'name'                      => 'Auditor',
            'permissionByCategoryGroup' =>
            [
                'Audit',
                'EPP Registry',
                'Billing',
            ],
            'excludedPermissions' =>
            [],
            'additionalPermissions' =>
            [],
        ],
        // [
        //     'name'                      => 'User Manager',
        //     'permissionByCategoryGroup' =>
        //     [
        //         'User Management'
        //     ], 
        //     'excludedPermissions' =>
        //     [

        //     ], 
        //     'additionalPermissions' => 
        //     [

        //     ],       
        // ],
        [
            'name'                      => 'Client Manager',
            'permissionByCategoryGroup' =>
            [
                'Client Management'
            ],
            'excludedPermissions' =>
            [],
            'additionalPermissions' =>
            [],
        ],
        [
            'name'                      => 'Domain Manager',
            'permissionByCategoryGroup' =>
            [
                'Domain Management',
                'Marketplace',
                'Offers'
            ],
            'excludedPermissions' =>
            [],
            'additionalPermissions' =>
            [],
        ],
        [
            'name'                      => 'System Manager',
            'permissionByCategoryGroup' =>
            [
                'Job Queues',
                'Notification Management',
                'Settings',
                'System Credits',
                'System Emails'
            ],
            'excludedPermissions' =>
            [],
            'additionalPermissions' =>
            [],
        ],
        [
            'name'                      => 'Viewer',
            'permissionByCategoryGroup' =>
            [],
            'excludedPermissions' =>
            [],
            'additionalPermissions' =>
            [
                'activity.admin',
                'activity.client',
                'client',
                'client.domains',
                'client.extension.fee',
                'client.transaction',
                'client.transaction.view',
                'client.transaction.view-total',
                'client.transaction.threshold',
                'client.logs',
                'client.sessions',
                'client.invite',
                'system.credits',
                'epp.account',
                'epp.poll',
                'epp.log',
                'job',
                //'notification.general',
                'notification.management',
                'notification.management.users',
                'setting.general',
                'setting.fee',
                'setting.extension.fee',
                'setting.transaction.threshold',
                'setting.commission',
                'email.history',
                'domain.history',
                'domains',
                'audits',
                'market_manuals',
                'offers',
                // 'user-management.admin',
                // 'user-management.role',
                // 'user-management.category',
                // 'user-management.settings',
                'domain.pending-delete.view',
                'billing.client',
                'billing.wire.transfer',
                'system.logs',
            ],
        ],
    ];
}
