<?php

namespace App\Modules\UserManagement\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Admin\Services\AdminInvitationService;
use App\Modules\UserManagement\Requests\UserManagementAdminIndexRequest;
use App\Modules\UserManagement\Requests\UserManagementAdminUpdateRequest;
use App\Modules\UserManagement\Requests\UserManagementAdminCreateRequest;
use App\Modules\UserManagement\Services\UserManagementAdminService; 

use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class UserManagementAdminController extends Controller
{
    public function index(UserManagementAdminIndexRequest $request)
    {
        $data = (new UserManagementAdminService())->fetchItems($request->only('showItems', 'name', 'email', 'status', 'orderBy'));

        return Inertia::render(
            'UserManagement/Admin/UserManagementAdminIndex', 
            ['data' => $data]
        );
    }

    public function create()
    {
        $data = (new UserManagementAdminService())->loadCreateFormData();
                
        return Inertia::render(
            'UserManagement/Admin/UserManagementAdminCreate',
            compact('data')
        );
    }

    public function store(UserManagementAdminCreateRequest $request)
    {
        (new UserManagementAdminService())->createItem($request->only('name', 'email', 'roleId', 'permissions'));

        return redirect()->route('user-management.admin')
            ->with('successMessage', 'User created.');
    }

    public function edit(int $id)
    {
        $data = (new UserManagementAdminService())->loadEditFormData($id);

        return Inertia::render(
            'UserManagement/Admin/UserManagementAdminUpdate',
            compact('data')
        );
    }

    public function update(UserManagementAdminUpdateRequest $request, int $id)
    {
        (new UserManagementAdminService())->updateItem($request->only('name', 'email', 'roleId', 'permissions') ,$id);

        return redirect()->route('user-management.admin')
            ->with('successMessage', 'User Updated');
    }

    public function fetchPermissions(int $id)
    {
        return (new UserManagementAdminService())->fetchItemPermissions($id);
    }

    public function delete(int $id)
    {
        (new UserManagementAdminService())->deleteItem($id);
    }

    public function resendInvitation(int $id)
    {
        (new AdminInvitationService())->resendEntry($id);
    }

    public function enable(int $id)
    {
        (new UserManagementAdminService())->enableItem($id);
    }

    public function disable(int $id) 
    {
        (new UserManagementAdminService())->disableItem($id);
    }
}
