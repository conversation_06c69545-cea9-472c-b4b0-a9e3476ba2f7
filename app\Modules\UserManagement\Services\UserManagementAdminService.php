<?php

namespace App\Modules\UserManagement\Services;

use App\Modules\CustomLogger\Services\AuthLogger;

use App\Modules\Admin\Constants\AdminStatusConstants;
use App\Modules\Admin\Services\AdminInvitationService;
use App\Traits\CursorPaginate;

use Exception; 

use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

use App\Events\AdminActionEvent;
use App\Modules\AdminHistory\Constants\HistoryType;

class UserManagementAdminService
{
    use CursorPaginate;

    /**
     * Fetch Items 
     * 
     * @param array $data
     */
    public function fetchItems (array $data)
    {
        $allItems = DB::table('admins')
            //->where('is_super_admin', '=', false)
            ->where('id', '!=', 1)
            ->where('id', '!=', Auth::user()->id)
            ->pluck('id')
            ->toArray();

        $builder =  DB::table("admins")
            ->select(
                'admins.id as adminId',
                'admins.name as adminName',
                'admins.email as adminEmail',
                'admins.status as adminStatus',
                'admins.last_active as adminLastActive',
                'admins.created_at as adminCreatedAt',
                'roles.id   as roleId',
                'roles.name as roleName',
            )
            ->selectSub(
                function ($query) {
                    $query->from('admin_access')
                        ->selectRaw('COUNT(*)')
                        ->whereColumn('admin_access.admin_id', 'admins.id');
                },
                'permissionCount'
            )
            ->leftJoin('admin_roles', 'admin_roles.admin_id', '=', 'admins.id')
            ->leftJoin('roles', 'roles.id', '=', 'admin_roles.role_id')
            ->where('admins.id', '!=', 1)
            ->where('admins.id', '!=', Auth::user()->id);

        $builder = $builder->when(
            isset($data['name']), 
            function (Builder $query) use ($data) 
            {
                $query->where('admins.name', 'ilike', $data['name'] . '%');
            }
        );

        $builder = $builder->when(
            isset($data['email']), 
            function (Builder $query) use ($data) 
            {
                $query->where('admins.email', 'ilike', $data['email'] . '%');
            }
        );

        $builder = $builder->when(
            isset($data['status']),
            function (Builder $query) use ($data) 
            {                
                $query->where('admins.status', '=', strtoupper($data['status']));
            }
        );

        $builder = $builder->when(
            isset($data['orderBy']), 
            function (Builder $query) use ($data) 
                {
                    $orderby = explode(':', $data['orderBy']);
                    $orderby = array_map('strtolower',$orderby);
                    $orderby = array_map('trim',$orderby);

                    switch ($orderby[0]) 
                    {
                        case 'name':
                            $query->orderBy('admins.name', $orderby[1]);
                            break;
                        case 'email':
                            $query->orderBy('admins.email', $orderby[1]);
                            break;
                        case 'status':
                            $query->orderBy('admins.status', $orderby[1]);
                            break;
                        case 'created at':
                            $query->orderBy('admins.created_at', $orderby[1]);
                            break;
                        case 'permissions':
                            $query->orderBy('permissionCount', $orderby[1]);
                            break;
                    }
                }
            )
            ->when(
            !isset($data['orderBy']),
            function (Builder $query) use ($data) 
                {
                    $query->orderBy('admins.created_at', 'desc');
                }
            );

        $builder = $builder->paginate($data['showItems'] ?? 10 )
            ->withQueryString();
    
        return CursorPaginate::cursor(
            $builder, 
            $this->paramToURI($data),
            compact('allItems')
        );
    }

    private static function paramToURI($data)
    {
        $param = [];

        if (isset($data['category'])) 
        {
            $param[] = 'category=' . $data['category'];
        }

        if (isset($data['orderBy'])) 
        {
            $param[] = 'orderBy=' . $data['orderBy'];
        }

        return $param;
    }

    /**
     * Fetch Item
     */
    public function loadCreateFormData()
    {
        $activePermissions = DB::table('access_category')
            ->join('access', 'access_category.access_id', '=', 'access.id')
            ->select(
                'access.id as permissionId',
                'access.name as permissionName'
                )
            ->distinct()
            ->get();


        $roles = DB::table('roles')->get();

        $categories = DB::table('category')
            ->select('id', 'name')
            ->orderBy('name')
            ->get();

        $categoryPermissions = (new UserManagementPermissionService())->fetchItemsGroupByCategory();

        return [
            'activePermissions'   => $activePermissions,
            'roles'               => $roles,
            'categories'          => $categories,
            'categoryPermissions' => $categoryPermissions
        ];
    }

    /**
     * Fetch Item
     * 
     * @param int $adminId
     */
    public function loadEditFormData(int $adminId)
    {
        $admin = DB::table('admins')
            ->select(
                'admins.id as adminId',
                'admins.name as adminName', 
                'admins.email as adminEmail',
                'roles.id as roleId',
                'roles.name as roleName' 
            )
            ->leftJoin('admin_roles', 'admin_roles.admin_id', '=', 'admins.id')
            ->leftJoin('roles', 'roles.id', '=', 'admin_roles.role_id')
            ->where('admins.id', '=', $adminId)
            ->where('admins.id', '!=', 1)
            ->where('admins.id', '!=', Auth::user()->id)
            ->firstOrFail();

        $initialPermissions = DB::table('access')
            ->join('admin_access', 'admin_access.access_id', '=', 'access.id')
            ->where('admin_access.admin_id', '=', $adminId)
            ->pluck('access.id')
            ->toArray();

        $activePermissions = DB::table('access_category')
            ->join('access', 'access_category.access_id', '=', 'access.id')
            ->select(
                'access.id as permissionId',
                'access.name as permissionName'
            )
            ->distinct()
            ->get();

        $roles = DB::table('roles')->get();

        $categories = DB::table('category')
            ->select('id', 'name')
            ->orderBy('name')
            ->get();

        $categoryPermissions =  (new UserManagementPermissionService())->fetchItemsGroupByCategory();

        return [
            'admin'               => $admin,
            'initialPermissions'  => $initialPermissions,
            'activePermissions'   => $activePermissions,
            'roles'               => $roles,
            'categories'          => $categories,
            'categoryPermissions' => $categoryPermissions
        ];
    }

    /**
     * Fetch Item Permissions 
     * 
     * @param int $id
     */
    public function fetchItemPermissions(int $id)
    {
        $admin = DB::table('admins')
            ->where('id', $id)
            ->first();

        $permissions =  DB::table('admin_access')
            ->select('access.name')
            ->join('access',  'admin_access.access_id', '=','access.id')
            ->where('admin_access.admin_id', $id)
            ->get();

        return [
            'name'        => $admin->name,
            'permissions' => $permissions
        ];
    }

    /**
     * Create Item
     *
     * @param array $data
     */
    public function createItem(array $data)
    {
        try
        {
            DB::transaction(
                function () use ($data)
                {
                    $password = Str::random(16);

                    $newUserId  = DB::table('admins')
                        ->insertGetId(
                            [
                                'name'       => $data['name'],
                                'email'      => $data['email'],
                                'password'   => Hash::make($password),
                                'created_at' => now()
                            ]
                        );

                    if (isset($data['roleId']))
                    {
                        $role = DB::table('roles')->where('id', $data['roleId'])->first();

                        DB::table('admin_roles')->insert(['role_id' => $role->id, 'admin_id' =>  $newUserId]);
                    }

                    $permissions = [];

                    foreach ($data['permissions'] as $permission)
                    {
                        $permissions[] = ['access_id' => $permission, 'admin_id' => $newUserId, 'created_at' => now(), 'updated_at' => now()];
                    }

                    DB::table('admin_access')->insert($permissions);

                    (new AdminInvitationService())->createEntry(
                        [
                            'name'   => $data['name'],
                            'userId' => $newUserId,
                            'email'  => $data['email'],
                        ]
                    );

                    $role = isset($data['roleId']) ? DB::table('roles')->where('id', $data['roleId'])->value('name') : 'None';
                    $permissionCount = count($data['permissions'] ?? []);
                    event(new AdminActionEvent(
                        auth()->user()->id,
                        HistoryType::USER_MANAGEMENT,
                        "Admin created: {$data['name']} ({$data['email']}) with ID {$newUserId} by " . auth()->user()->email . " - Role: {$role}, Permissions: {$permissionCount}"
                    ));

                    app(AuthLogger::class)->info("admin {$data['name']} |  {$data['email']} with ID Number {$newUserId} created");
                }
            );
        }
        catch(Exception $error)
        {
            app(AuthLogger::class)->error("admin {$data['name']} |  {$data['email']} could not be created {$error->getMessage()}");
        }
    }

    /**
     * Update Item
     *
     * @param array $data
     * @param int   $adminId
     */
    public function updateItem(array $data, int $adminId)
    {
        try
        {
            DB::transaction(
                function () use ($data, $adminId)
                {
                    $admin = DB::table('admins')
                        ->where('id', '=', $adminId)
                        ->firstOrFail();

                    DB::table('admins')
                        ->where('id', '=', $admin->id)
                        ->update(
                            [
                                'name'       => $data['name'],
                                'email'      => $data['email'],
                                'updated_at' => now()
                            ]
                        );

                    DB::table('admin_roles')
                        ->where('admin_id', '=', $admin->id)
                        ->delete();

                    if (isset($data['roleId']))
                    {
                        $role = DB::table('roles')->where('id', $data['roleId'])->first();

                        DB::table('admin_roles')->insert(['role_id' => $role->id, 'admin_id' => $admin->id]);
                    }

                    DB::table('admin_access')
                        ->where('admin_id', '=', $admin->id)
                        ->delete();

                    $permissions = [];

                    foreach ($data['permissions'] as $permission)
                    {
                        $permissions[] = ['access_id' => $permission, 'admin_id' => $admin->id, 'created_at' => now(), 'updated_at' => now()];
                    }

                    DB::table('admin_access')->insert($permissions);

                    //! TO IMMEDIATELY REFLECT CHANGES
                    Cache::forget("user_permissions_{$admin->id}");

                    // Log admin action with request data
                    $role = isset($data['roleId']) ? DB::table('roles')->where('id', $data['roleId'])->value('name') : 'None';
                    $permissionCount = count($data['permissions'] ?? []);
                    event(new AdminActionEvent(
                        auth()->user()->id,
                        HistoryType::USER_MANAGEMENT,
                        "Admin updated: {$data['name']} ({$data['email']}) with ID {$adminId} by " . auth()->user()->email . " - Role: {$role}, Permissions: {$permissionCount}"
                    ));

                    app(AuthLogger::class)->info("admin {$data['name']} |  {$data['email']} with ID Number {$admin->id} updated");
                }
            );
        }
        catch(Exception $error)
        {
            app(AuthLogger::class)->error("admin with ID {$adminId} could not be updated {$error->getMessage()}");
        }
    }

    /**
     * Delete Item
     * 
     * @param int $adminId
     */
    public function deleteItem(int $adminId)
    {
        $admin = DB::table('admins')
            ->where('id', '=', $adminId)
            ->firstOrFail();

        DB::table('admins')
            ->where('id', '=', $adminId)
            ->delete();
    
        app(AuthLogger::class)->info("user {$admin->name} with ID Number {$admin->id} deleted");
    }

    /**
     * Enable Item
     * 
     * @param int $adminId 
     */
    public function enableItem(int $adminId)
    {
        $admin = DB::table('admins')
            ->where('id', '=', $adminId)
            ->firstOrFail();

        DB::table('admins')
            ->where('id', '=', $adminId)
            ->update(
                [
                    'status' => AdminStatusConstants::STATUS_ACTIVE
                ]
            );

        app(AuthLogger::class)->info("user {$admin->name} with ID Number {$admin->id} enabled");
    }

    /**
     * Disable Item
     * 
     * @param int $adminId 
     */
    public function disableItem(int $adminId)
    {
        $admin = DB::table('admins')
            ->where('id', '=', $adminId)
            ->firstOrFail();

        DB::table('admins')
            ->where('id', '=', $adminId)
            ->update(
                [
                    'status' => AdminStatusConstants::STATUS_DISABLED
                ]
            );

        app(AuthLogger::class)->info("user {$admin->name} with ID Number {$admin->id} disabled");
    }
}
