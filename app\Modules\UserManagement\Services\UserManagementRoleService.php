<?php

namespace App\Modules\UserManagement\Services;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\UserManagement\Services\UserManagementPermissionService;
use App\Traits\CursorPaginate;

use Exception; 


use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Query\Builder;
use App\Events\AdminActionEvent;
use App\Modules\AdminHistory\Constants\HistoryType;

class UserManagementRoleService
{
    use CursorPaginate;

    /**
     * Fetch Items 
     * 
     * @param array $data
     */
    public function fetchItems(array $data)
    {
        $allItems = DB::table('roles')->pluck('id')->toArray();

        $builder = DB::table('roles')
            ->join('access_roles', 'access_roles.role_id', '=', 'roles.id')
            ->join('admins', 'roles.user_id', "=", 'admins.id');

            
        $builder = $builder->when(
            isset($data['role']),
            function (Builder $query) use ($data) 
            {
                $query->where('roles.name', 'ilike', $data['role'] . '%');
            }
        );

        $builder = $builder->groupBy('roles.id', 'admins.name');

        $builder = $builder->when(
            isset($data['orderBy']),
            function (Builder $query) use ($data) 
            {
                $orderby = explode(':', $data['orderBy']);
                $orderby[1] = trim($orderby[1]);
                switch ($orderby[0]) 
                {
                    case 'Role':
                        $query->orderBy('roles.name', $orderby[1]);
                        break;
                    case 'Created By':
                        $query->orderBy('admins.name', $orderby[1]);
                        break;
                    case 'Last Updated':
                        $query->orderBy('roles.updated_at', $orderby[1]);
                        break;
                    case 'Permissions':
                        $query->orderBy(DB::raw('COUNT(access_roles.role_id)'), $orderby[1]);
                        break;
                }
            }
        )
            ->when(
                !isset($data['roles']),
                function (Builder $query) use ($data) 
                {
                    $query->orderBy('roles.created_at', 'desc');
                }
            );

        $builder = $builder->select(
            'roles.id',
                'roles.name as role',
                'roles.updated_at as lastUpdated',
                DB::raw('COUNT(access_roles.role_id) AS access'),
                'admins.name AS createdBy'
            )
            ->paginate($data['showItems'] ?? 10)
            ->withQueryString();

        return CursorPaginate::cursor(
            $builder,
            $this->paramToURI($data),
            compact('allItems')
        );
    }

    private static function paramToURI($data)
    {
        $param = [];

        if (isset($data['role'])) {
            $param[] = 'role' . $data['role'];
        }

        if (isset($data['orderBy'])) {
            $param[] = 'orderBy=' . $data['orderBy'];
        }

        return $param;
    }

    /**
     * Load Create Form Data
     */
    public function lordCreateFormData()
    {
        return [
            'categories'          => (new UserManagementCategoryService())->fetchCategories(),
            'allPermissions'      => (new UserManagementPermissionService())->fetchItems(),
            'activePermissions'   => (new UserManagementPermissionService())->fetchItemsActiveByCategory(),
            'categoryPermissions' => (new UserManagementPermissionService())->fetchItemsGroupByCategory()
        ];
    }

    /**
     * Load Edit Form Data
     * 
     * @param int $id
     */
    public function lordEditFormData($id)
    {
        $categories          = (new UserManagementCategoryService())->fetchCategories();
        $categoryPermissions = (new UserManagementPermissionService())->fetchItemsGroupByCategory();
        $allPermissions      = (new UserManagementPermissionService())->fetchItems();
        $activePermissions   = (new UserManagementPermissionService())->fetchItemsActiveByCategory();

        $assignedPermissions = DB::table('access_roles AS ar')
            ->join('access AS a', 'ar.access_id', 'a.id')
            ->select('a.name', 'a.id')
            ->where('role_id', $id)
            ->get();

        $role = DB::table('roles')
            ->where('id', $id)
            ->firstOrFail();

        return [
            'categories'          => $categories,
            'allPermissions'      => $allPermissions,
            'activePermissions'   => $activePermissions,
            'categoryPermissions' => $categoryPermissions,
            'role'                => $role,
            'assignedPermissions' => $assignedPermissions,
        ];
    }

    /**
     * Fetch Item Permissions 
     * 
     * @param int $id
     */
    public function fetchItemPermissions(int $id)
    {
        $role = DB::table('roles')
            ->where('id', $id)
            ->first();

        $permissions =  DB::table('access_roles AS ac')
            ->join('access as a', 'ac.access_id', 'a.id')
            ->select('a.name')
            ->where('role_id', $id)
            ->get();

        return [
            'name'        => $role->name,
            'permissions' => $permissions
        ];
    }

    /**
     * Create Item
     * 
     * @param array $data 
     */
    public function createItem(array $data)
    {
        try 
        {
            DB::transaction(
                function () use ($data)
                {
                    $permissions = [];
                    $now         = now();
                    $userId      = Auth::user()->id;
                    $id          = DB::table('roles')->insertGetId(['name' => $data['name'], 'user_id' => $userId, 'created_at' => $now, 'updated_at' => $now]);

                    foreach ($data['permissions'] as $permission) 
                    {
                        $permissions[] = ['access_id' => $permission, 'role_id' => $id, 'created_at' => $now, 'updated_at' => $now];
                    }

                    DB::table('access_roles')->insert($permissions);

                    $permissionCount = count($data['permissions'] ?? []);
                    event(new AdminActionEvent(
                        auth()->user()->id,
                        HistoryType::USER_MANAGEMENT,
                        "Role created: {$data['name']} with ID {$id} by " . auth()->user()->email . " - Permissions: {$permissionCount}"
                    ));
                }
            );
        }
        catch (Exception $error)
        {
            app(AuthLogger::class)->error("Could not create Role {$data['name']} {$error->getMessage()}");
        }
    }

    /**
     * Update Item
     * 
     * @param array $data
     * @param int   $roleId 
     */
    public function updateItem(array $data, int $roleId)
    {    
        try 
        {
            DB::transaction(
                function () use ($data, $roleId)
                {
                    $permissions = [];
                    $now         = now();

                    DB::table('access_roles')->where('role_id', $roleId)->delete();
                    DB::table('roles')->where('id', $roleId)->update(['name' => $data['name'], 'updated_at' => $now]);

                    foreach ($data['permissions'] as $permission) {
                        $permissions[] = ['access_id' => $permission, 'role_id' => $roleId, 'created_at' => $now, 'updated_at' => $now];
                    }

                    DB::table('access_roles')->insert($permissions);

                    if (!empty($data['syncPermissions']))
                    {
                        $this->syncUserPermissionsWithRole($roleId);
                    }

                    app(AuthLogger::class)->info("role {$data['name']} with ID Number {$roleId} updated");

                    $permissionCount = count($data['permissions'] ?? []);
                    $syncStatus = !empty($data['syncPermissions']) ? 'with user sync' : 'without user sync';
                    event(new AdminActionEvent(
                        auth()->user()->id,
                        HistoryType::USER_MANAGEMENT,
                        "Role updated: {$data['name']} with ID {$roleId} by " . auth()->user()->email . " - Permissions: {$permissionCount} ({$syncStatus})"
                    ));
                }
            );
        }
        catch (Exception $error)
        {
            app(AuthLogger::class)->error("Role with ID Number {$roleId} could not updated {$error->getMessage()}");
        }
    }

    /**
     * Sync User Permissions
     * 
     * @param int $roleId
     */
    public function syncUserPermissionsWithRole(int $roleId)
    {
        $permissions = DB::table('access_roles')
            ->where('role_id', '=', $roleId)
            ->pluck('access_id')
            ->toArray(); 

        $admins = DB::table('admin_roles')
            ->where('role_id', '=', $roleId)
            ->pluck('admin_id')
            ->toArray();

        foreach ($admins as $admin)
        {
            DB::table('admin_access')
                ->where('admin_id', '=', $admin)
                ->delete(); 
        
            $permissionsToBeInserted = []; 

            foreach ($permissions as $permission)
            {
                $permissionsToBeInserted[] = 
                [
                    'admin_id'  => $admin,
                    'access_id' => $permission
                ];
            }

            DB::table('admin_access')->insert($permissionsToBeInserted);
            
            //! TO IMMEDIATELY REFLECT CHANGES
            Cache::forget("user_permissions_{$admin}");
        }
    }

    /**
     * Delete Item
     * 
     * @param int $id
     */
    public function deleteItem(int $id)
    {
        $roles = DB::table('roles')
            ->where('id', '=', $id)
            ->firstOrFail();

        DB::table('roles')
            ->where('id', '=', $id)
            ->delete();

        app(AuthLogger::class)->info("role {$roles->name} with ID Number {$roles->id} deleted");

        event(new AdminActionEvent(
            auth()->user()->id,
            HistoryType::USER_MANAGEMENT,
            "Role deleted: {$roles->name} with ID {$roles->id} by " . auth()->user()->email
        ));
    }

    /**
     * Bulkk Delete Items
     *
     * @param array $data
     */
    public function bulkDeleteItems(array $data)
    {
        $roleNames = [];

        foreach ($data['roles'] as $roleId)
        {
            $roles = DB::table('roles')->where('id', $roleId)->firstOrFail();
            $roleNames[] = $roles->name;

            DB::table('access_roles')
                ->where('role_id', $roles->id)
                ->delete();

            DB::table('roles')
                ->where('id', '=', $roles->id)
                ->delete();

            app(AuthLogger::class)->info("role {$roles->name} with ID Number {$roles->id} deleted");
        }

        $roleCount = count($data['roles']);
        event(new AdminActionEvent(
            auth()->user()->id,
            HistoryType::USER_MANAGEMENT,
            "Roles deleted: {$roleCount} roles (" . implode(', ', $roleNames) . ") by " . auth()->user()->email
        ));
    }
}
