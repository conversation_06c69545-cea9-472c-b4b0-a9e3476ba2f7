//* PACKAGES
import React from 'react';

//* ICONS
import { FaEye, FaRegThumbsUp, FaRegThumbsDown, FaRegHourglass, FaRegQuestionCircle } from 'react-icons/fa';

//* COMPONENTS
//...

//* PARTIALS
//...

//* STATE
//...

//* HOOKS
//...

//* UTILS
//... 

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...


export default function BankTransferStatusComponent(
    {
        item
    }
)
{
    //! PACKAGE
    //...    

    //! HOOKS
    //...

    //! VARIABLES
    //...

    //! STATES
    //... 

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    const displayStatus = () =>
    {
        let classNamesCommon   = "flex items-center gap-2 uppercase font-semibold"
        let classNamesSpecific = ""
        let icon               = <FaRegHourglass />;
        let status             = 'pending';

        if (item.bankTransferDeletedAt)
        {
            classNamesSpecific = 'text-danger';
            icon               = <FaRegThumbsDown />;
            status             = "rejected";
        }
        else if (item.bankTransferVerifiedAt)
        {
            classNamesSpecific = 'text-success';
            icon               = <FaRegThumbsUp />;
            status             = "verified";
        }
        else if (item.bankTransferReviewedAt && !item.bankTransferDeletedAt)
        {
            classNamesSpecific = 'text-orange-500';
            icon               = <FaRegQuestionCircle />;
            status             = "unverified";
        }
        else if (!item.bankTransferReviewedAt && !item.bankTransferDeletedAt)
        {
            classNamesSpecific = 'text-slate-500';
            icon               = <FaRegHourglass />;
            status             = "pending";
        }

        return (
            <div
                className={`
                    ${classNamesCommon} 
                    ${classNamesSpecific}     
                `}
            >
                <span>
                    {status}
                </span>
                {icon}
            </div>
        );
    };

    return (
        <>
            {displayStatus()}   
        </>
    );
};
