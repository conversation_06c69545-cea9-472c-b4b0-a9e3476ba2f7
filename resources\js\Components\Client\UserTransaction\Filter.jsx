import { _Transactions } from "@/Constant/_Transactions";
import { useForm } from "@inertiajs/react";

export default function Filter({ url }) {
    const CONFIG = {
        filterType: {
            year: "year",
            month: "month",
            fromDate: "fromDate",
            toDate: "toDate",
            user: "user",
            transactionType: "transactionType",
        },
        years: ["All", "2021", "2022", "2023", "2024", "2025", "2026", "2027", "2028", "2029", "2030"],
        months: [
            "All", "January", "February", "March", "April", "May", "June",
            "July", "August", "September", "October", "November", "December"
        ],
        transactionTypes: _Transactions.names,
    };

    const { data, setData, get, reset } = useForm({
        year: "All",
        month: "All",
        fromDate: "",
        toDate: "",
        user: "",
        transactionType: "all",
    });

    const handleChange = (e) => {
        setData(e.target.name, e.target.value);

        if (e.target.name === CONFIG.filterType.year || e.target.name === CONFIG.filterType.month) {
            reset("toDate");
        } else if (e.target.name === CONFIG.filterType.fromDate || e.target.name === CONFIG.filterType.toDate) {
            reset("year");
        }
    };

    const handleApply = (e) => {
        e.preventDefault();
        get(url, { preserveState: true, preserveScroll: true });
    };

    const handleReset = () => {
        reset();
        get(url, { preserveState: true, preserveScroll: true });
    };

    return (
        <div className="bg-white border border-gray-200 shadow-sm hover:shadow-md rounded-xl p-4">
            <form onSubmit={handleApply} className="space-y-4">
                {/* Filter Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    <div className="flex items-center space-x-2">
                        <label className="text-gray-600 whitespace-nowrap">Filter by:</label>
                        <select
                            name={CONFIG.filterType.year}
                            value={data.year}
                            onChange={handleChange}
                            className="border rounded-lg py-1 px-2 text-sm w-full"
                        >
                            {CONFIG.years.map((yr, index) => (
                                <option key={index} value={yr}>
                                    {yr}
                                </option>
                            ))}
                        </select>
                    </div>

                    <div className="flex items-center space-x-2">
                        <label className="text-gray-600 whitespace-nowrap">Month:</label>
                        <select
                            name={CONFIG.filterType.month}
                            value={data.month}
                            onChange={handleChange}
                            className="border rounded-lg py-1 px-2 text-sm w-full"
                        >
                            {CONFIG.months.map((m, index) => (
                                <option key={index} value={m}>
                                    {m}
                                </option>
                            ))}
                        </select>
                    </div>

                    <div className="flex items-center space-x-2">
                        <label className="text-gray-600 whitespace-nowrap">From:</label>
                        <input
                            type="date"
                            name={CONFIG.filterType.fromDate}
                            value={data.fromDate}
                            onChange={handleChange}
                            className="border rounded-lg px-2 py-1 text-sm w-full"
                        />
                    </div>

                    <div className="flex items-center space-x-2">
                        <label className="text-gray-600 whitespace-nowrap">To:</label>
                        <input
                            type="date"
                            name={CONFIG.filterType.toDate}
                            value={data.toDate}
                            onChange={handleChange}
                            className="border rounded-lg px-2 py-1 text-sm w-full"
                        />
                    </div>

                    <div className="flex items-center space-x-2">
                        <label className="text-gray-600 whitespace-nowrap">User:</label>
                        <input
                            type="text"
                            name={CONFIG.filterType.user}
                            value={data.user}
                            onChange={handleChange}
                            placeholder="Search name or email…"
                            className="border rounded-lg px-2 py-1 text-sm w-full"
                        />
                    </div>

                    <div className="flex items-center space-x-2">
                        <label className="text-gray-600 whitespace-nowrap">Type:</label>
                        <select
                            name={CONFIG.filterType.transactionType}
                            value={data.transactionType}
                            onChange={handleChange}
                            className="border rounded-lg py-1 px-2 text-sm w-full"
                        >
                            <option value="all">All Types</option>
                            {Object.entries(CONFIG.transactionTypes).map(([key, value], index) => (
                                <option key={index} value={key}>
                                    {value}
                                </option>
                            ))}
                        </select>
                    </div>
                </div>

                {/* Actions */}
                <div className="flex space-x-3">
                    <button
                        type="submit"
                        className="bg-blue-500 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-600 transition"
                    >
                        Apply
                    </button>
                    <button
                        type="button"
                        onClick={handleReset}
                        className="bg-gray-200 text-gray-700 px-4 py-2 rounded-lg text-sm hover:bg-gray-300 transition"
                    >
                        Reset
                    </button>
                </div>
            </form>
        </div>

    );
}
