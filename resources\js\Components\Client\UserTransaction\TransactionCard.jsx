//* PACKAGES
import React, { useState, useEffect, useRef } from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';
import "react-toastify/dist/ReactToastify.css";

//* ICONS
import { MdRestartAlt } from "react-icons/md";

//* COMPONENTS
import TextInput from "@/Components/TextInput";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 

//* UTILS
import { getEventValue } from "@/Util/TargetInputEvent";
import { _Transactions } from '@/Constant/_Transactions';
import TransactionModal from './TransactionModal';

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function TransactionCard({ type = null, data = [], isTotalTransaction = false, handleShowModal = () => { } }) {
    //! PACKAGE
    //...

    //! HOOKS
    //...

    //! VARIABLES
    const title = isTotalTransaction ? "Total Transactions" : _Transactions.names[type];
    const note = "Healthy";
    //...

    //! STATES
    const [showModal, setShowModal] = useState(false);

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    const summary = isTotalTransaction ? data.reduce((acc, item) => {
        acc.count += item.count || 0;
        acc.hits += item.hits || 0;
        acc.approved += item.approved || 0;
        acc.rejected += item.rejected || 0;
        return acc;
    }, { count: 0, hits: 0, approved: 0, rejected: 0 }) : data;
    //...

    return (
        <>
            <div
                title={title}
                className="bg-white border border-gray-200/50 shadow-sm hover:shadow-md rounded-xl flex flex-col justify-between cursor-pointer space-y-2"
                onClick={() => handleShowModal(type)}
            >
                <div className='p-4'>
                    <h4 className="text-sm truncate">{title}</h4>
                    <p className="text-2xl font-bold">{summary.count}</p>
                </div>
                <div className={`mt-2 flex justify-between py-1 px-4 rounded-b-xl ${note === "Healthy" ? "bg-green-100 text-green-600" :
                    note === "Warning" ? "bg-yellow-100 text-yellow-600" :
                        "bg-gray-100 text-gray-600"
                    }`}>
                    <span className="flex justify-center items-center text-xs space-x-2">
                        <span
                            className={`w-2 h-2 rounded-full ${note === "Healthy" ? "bg-green-500" : note === "Warning" ? "bg-yellow-500" : "bg-gray-400"}`}
                        />
                        <span>{note}</span>
                    </span>
                    <p className="text-xs font-bold text-gray-500 mt-1">
                        <span >{`${summary.hits} hits (`}</span>
                        <span className='text-success'>{`${summary.approved}S`}</span>
                        <span >{" / "}</span>
                        <span className='text-red-500'>{`${summary.rejected}R`}</span>
                        <span >{")"}</span>
                    </p>
                </div>
            </div>
            {/* <TransactionModal
                data={data}
                type={type}
                isModalOpen={showModal}
                closeModal={() => setShowModal(false)}
                isTotalTransaction={isTotalTransaction}
            /> */}
        </>
    );
}
