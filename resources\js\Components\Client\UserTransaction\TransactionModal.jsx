//* PACKAGES
import React from 'react'
import "react-toastify/dist/ReactToastify.css";

//* ICONS
import { MdClose, MdRemoveRedEye } from "react-icons/md";

//* COMPONENTS

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 

//* UTILS
import { _Transactions } from '@/Constant/_Transactions';
import Modal from '@/Components/Modal';
import SecondaryButton from '@/Components/SecondaryButton';
import { useMemo } from 'react';
import { useState } from 'react';
import LoaderSpinner from '@/Components/LoaderSpinner';

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function TransactionModal({
    data = [],
    type = 'REGISTER',
    isModalOpen = false,
    isLoading = true,
    closeModal
}) {
    //! PACKAGE
    //...
    //! HOOKS
    //! VARIABLES
    const title = !type
        ? "Total Transactions"
        : `User Details for ${_Transactions.names[type]}`;

    //...
    //! STATES
    //! USE EFFECTS
    //...
    //! FUNCTIONS
    const renderRows = () => {
        return data.map((item, idx) => (
            <tr key={idx} className="border-b last:border-0 hover:bg-gray-50">
                <td className="py-2 px-3">
                    {
                        !type
                            ? _Transactions.names[item.transaction_name]
                            : `${item.user_name} (${item.email})`
                    }
                </td>
                <td className="py-2 px-3">{item.count}</td>
                <td className="py-2 px-3 space-x-2">
                    <span>{item.hits}</span>
                    {item.hits > 0 && (
                        <span className="text-xs font-bold text-gray-500 mt-1">
                            <span >{"("}</span>
                            <span className='text-success'>{`${item.approved}S `}</span>
                            <span >{"/ "}</span>
                            <span className='text-red-500'>{`${item.rejected}R`}</span>
                            <span >{")"}</span>
                        </span>
                    )}
                </td>
                {/* <td className="py-2 px-3"><MdRemoveRedEye /></td> */}
            </tr>
        ));
    };
    //...

    return (
        <Modal
            className="flex flex-col gap-y-6 p-6 w-[600px] max-w-full text-sm"
            show={isModalOpen}
            onClose={closeModal}
            closeable={false}
        >
            {/* Header */}
            <div className="flex justify-between items-center border-b pb-3">
                <h2 className="text-lg font-semibold">{title}</h2>
                <button
                    onClick={closeModal}
                    className="text-gray-500 hover:text-gray-700"
                >
                    <MdClose className="w-4 h-4" />
                </button>
            </div>


            {/* Table */}
            <div>
                {isLoading
                    ? <div className="flex justify-center items-center py-4 space-x-3">
                        <LoaderSpinner h='h-8' w='w-8' />
                        <span>Loading Data...</span>
                    </div>
                    : !data || data.length === 0
                        ? <div className="py-3 px-3 text-center text-gray-500" colSpan={3}>No records found</div>
                        : <table className="w-full border-collapse text-sm">
                            <thead>
                                <tr className="text-left bg-gray-100">
                                    <th className="py-2 px-3">
                                        {!type ? "Transaction Type" : "User"}
                                    </th>
                                    <th className="py-2 px-3">Count</th>
                                    <th className="py-2 px-3">Threshold Hits</th>
                                    {/* <th className="py-2 px-3">Action</th> */}
                                </tr>
                            </thead>
                            <tbody>{renderRows()}</tbody>
                        </table>
                }
            </div>

            {/* Footer */}
            <div className="flex justify-end pt-4">
                <SecondaryButton onClick={closeModal}>Close</SecondaryButton>
            </div>
        </Modal>
    )
}
