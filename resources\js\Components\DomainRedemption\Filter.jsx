import ActiveFilter from "@/Components/Util/Filter/ActiveFilter";
import DisplayFilter from "@/Components/Util/Filter/DisplayFilter";
import OptionFilter from "@/Components/Util/Filter/OptionFilter";
import TextFilter from "@/Components/Util/Filter/TextFilter";
import useOutsideClick from "@/Util/useOutsideClick";
import { useRef, useState } from "react";
import {
    offFilter,
    updateFieldValue,
} from "@/Components/Util/Filter/FilterMethod";
import { router } from "@inertiajs/react";
import "react-toastify/dist/ReactToastify.css";


export default function Filter() {
    const params = route().params;
    const { orderby, user, email, name, deletedby} = params;

    const [emailInput, setEmailInput] = useState(email || "");
    const [nameInput, setNameInput] = useState(name || "");
    const [deletedByInput, setDeletedByInput] = useState(deletedby || "");

    const containerRef = useRef();

    const generateConfig = () => {
        const cfg = {
            container: {
                active: false,
                reload: false,
            },
            field: {
                orderby: {
                    active: false,
                    value: orderby ? [orderby] : [],
                    type: "option",
                    items: [
                        "Domain: Asc",
                        "Domain: Desc",
                        "Date Deleted: Asc",
                        "Date Deleted: Desc",
                    ],
                    name: "Order By",
                },
                name: {
                    active: false,
                    value: name ? [name] : [],
                    type: "text",
                    name: "Name",
                    tempValue: nameInput,
                },
                email: {
                    active: false,
                    value: email ? [email] : [],
                    type: "text",
                    name: "Email",
                    tempValue: emailInput,
                },
                deletedby: {
                    active: false,
                    value: deletedby ? [deletedby] : [],
                    type: "text",
                    name: "Deleted By",
                    tempValue: deletedByInput,
                },


            },
        };

        return cfg;
    };

    const [filter, setFilter] = useState(generateConfig());
    const { field } = filter;

    const excludedFilters = Object.keys(filter.field);
    const getOtherParam = () => {
        return Object.fromEntries(
            Object.entries(route().params).filter(([key]) => !excludedFilters.includes(key))
        );
    }

    useOutsideClick(containerRef, () => {
        setFilter((prevFilter) => {
            const updatedFilter = offFilter(prevFilter);
            return {
                ...updatedFilter,
                field: Object.keys(updatedFilter.field).reduce(
                    (acc, key) => ({
                        ...acc,
                        [key]: {
                            ...updatedFilter.field[key],
                            active: false,
                        },
                    }),
                    {}
                ),
            };
        });
    });

    const handleDisplayToggle = (newObject) => {
        const closedFilter = offFilter(filter);
        setFilter({
            ...closedFilter,
            ...newObject,
        });
    };

    const submit = (updatedFilter = filter) => {
        let filterFields = Object.keys(filter.field);
        let payload = getOtherParam()

        filterFields.forEach(element => {
            if (updatedFilter.field[element].value.length > 0) {
                payload[element] = updatedFilter.field[element].value[0];
            }
        });
        router.get(route("domain-redemption.view"), payload);
    };


    const handleFieldUpdateValue = (key, value, forceReload = false) => {
        const setInputMap = {
            email: setEmailInput,
            name: setNameInput,
            deletedby: setDeletedByInput
        };

        if (setInputMap[key]) {
            setInputMap[key](value);

            if (!value || value === filter.field[key].tempValue) {
                const newValue = updateFieldValue(value, {
                    ...filter.field[key],
                });
                const updatedFilter = {
                    ...filter,
                    container: { ...filter.container, active: false },
                    field: {
                        ...filter.field,
                        [key]: { ...newValue },
                    },
                };
                setFilter(offFilter(updatedFilter));
                submit(updatedFilter);
                return;
            }

            setFilter((prevFilter) => ({
                ...prevFilter,
                field: {
                    ...prevFilter.field,
                    [key]: {
                        ...prevFilter.field[key],
                        tempValue: value,
                    },
                },
            }));
            return;
        }

        const newValue = updateFieldValue(value, { ...filter.field[key] });
        const updatedFilter = {
            ...filter,
            container: { ...filter.container, active: false },
            field: {
                ...filter.field,
                [key]: { ...newValue },
            },
        };

        setFilter(offFilter(updatedFilter));
        submit(updatedFilter);
    };

    return (
        <div className="flex items-center relative" ref={containerRef}>
            <ActiveFilter
                field={field}
                handleFieldUpdateValue={handleFieldUpdateValue}
            />
            <div className="relative">
                <DisplayFilter
                    handleDisplayToggle={handleDisplayToggle}
                    container={filter.container}
                    field={filter.field}
                />

                <OptionFilter
                    fieldProp={field.orderby}
                    fieldKey="orderby"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
                <TextFilter
                    fieldProp={field.name}
                    fieldKey="name"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                    offFilter={() => {
                        const currentValue =
                            field.name.tempValue || field.name.value[0] || "";
                        handleFieldUpdateValue("name", currentValue);
                        setFilter(offFilter(filter));
                    }}
                    placeholder="Search name"
                />
                <TextFilter
                    fieldProp={field.email}
                    fieldKey="email"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                    offFilter={() => {
                        const currentValue =
                            field.email.tempValue || field.email.value[0] || "";
                        handleFieldUpdateValue("email", currentValue);
                        setFilter(offFilter(filter));
                    }}
                />

                {field.deletedby && (
                    <TextFilter
                        fieldProp={field.deletedby}
                        fieldKey="deletedby"
                        handleFieldUpdateValue={handleFieldUpdateValue}
                        offFilter={() => {
                            const currentValue =
                                field.deletedby.tempValue ||
                                field.deletedby.value[0] ||
                                "";
                            handleFieldUpdateValue("deletedby", currentValue);
                            setFilter(offFilter(filter));
                        }}
                        placeholder="Search email"
                    />
                )}
            </div>
        </div>
    );
}