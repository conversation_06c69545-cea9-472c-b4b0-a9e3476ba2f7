<?php

namespace App\Modules\Notification\Services;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Notification\Constants\NotificationType;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DomainNotificationService extends NotificationService
{
  public static function instance(): self
  {
      $domainNotificationService = new self;

      return $domainNotificationService;
  }

  private function sendNotification($field)
  {
    return DB::client()->table('notifications')->insert($field);
  }

  public function DomainReuqestCreated(string $domain, $userId): void
  {
    $this->sendNotification([
            'user_id' => $userId,
            'title' => 'Domain Deletion Request Created',
            'message' => 'A deletion request for your domain "' . $domain . '" has been created by admin and is being processed immediately.',
            'redirect_url' => 'domain',
            'importance' => NotificationType::IMPORTANT,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);
  }

}