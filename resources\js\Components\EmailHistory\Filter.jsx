import React from "react";
import ActiveFilter from "@/Components/Util/Filter/ActiveFilter";
import DisplayFilter from "@/Components/Util/Filter/DisplayFilter";
import OptionFilter from "@/Components/Util/Filter/OptionFilter";
import TextFilter from "@/Components/Util/Filter/TextFilter";
import { useRef, useState } from "react";
import {
    offFilter,
    updateFieldValue,
} from "@/Components/Util/Filter/FilterMethod";
import { router } from "@inertiajs/react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import useOutsideClick from "@/Util/useOutsideClick";

export default function Filter() {
    const { email, subject, date, emailType } = route().params;
    const containerRef = useRef();
    
    // Add state for email input
    const [emailInput, setEmailInput] = useState(email || "");
    const [subjectInput, setSubjectInput] = useState(subject || "");
    

    const config = {
        container: {
            active: false
        },

        field: {
            email: {
                active: false,
                value: emailInput ? [emailInput] : [],
                type: "text",
                name: "Recipient Email",
                // Add temporary value for display during typing
                tempValue: emailInput,
            },
            subject: {
                active: false,
                value: subjectInput ? [subjectInput] : [],
                type: "text",
                name: "Email Subject",
                tempValue: subjectInput,
            },
            date: {
                active: false,
                value: date ? [date] : [],
                type: "option",
                items: ["Today", "Yesterday", "Last 7 Days", "Last 30 Days"],
                name: "Date & Time Sent",
            },
            emailType: {
                active: false,
                value: emailType ? [emailType] : [],
                type: "option",
                items: [
                    "Domain Authentication Request",
                    "Domain Transfer Refund",
                    "Domain Redemption Notice",
                    "Third Expiration Notice",
                    "Second Expiration Notice",
                    "First Expiration Notice",
                    "Transfer Payment Invoice",
                    "Registration Payment Invoice",
                    "Renewal Payment Invoice",
                    "Redemption Payment Invoice",
                    "Verified Identity Verification Notice",
                    "In Process Identity Verification Notice",
                    "OTP Verification",
                    "Account Credit - Bank Transfer Notification",
                    "Account Credit - Added to Account",
                    "Domain Transfer - Outbound Request",
                    "Clients Query",
                    "Report Abuse",
                    "User Invite",
                    "Domain Registration Refund",
                ],
                name: "Email Type",
            },
        },
    };

    const [filter, setFilter] = useState(config);
    const { field } = filter;

    useOutsideClick(containerRef, () => {
        setFilter(prevFilter => {
            const updatedFilter = offFilter(prevFilter);
            return {
                ...updatedFilter,
                field: Object.keys(updatedFilter.field).reduce((acc, key) => ({
                    ...acc,
                    [key]: {
                        ...updatedFilter.field[key],
                        active: false
                    }
                }), {})
            };
        });
    });

    const submit = (updatedFilter) => {
        const { email, subject, date, emailType } = updatedFilter.field;
        let payload = route().params;

        if (email.value.length > 0 || email.tempValue=="") payload.email = email.value[0];
        if (subject.value.length > 0 || subject.tempValue=="") payload.subject = subject.value[0];
        payload.date = date.value[0] ?? undefined;
        payload.emailType = emailType.value[0] ?? undefined;

        router.get(route("email.history"), payload, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleDisplayToggle = (newObject) => {
        setFilter({ ...filter, ...newObject });
    };

    const handleFieldUpdateValue = (key, value, forceReload = false) => {
        if (key === "email") {
            setEmailInput(value);
            
            if (!value || value === emailInput) {
                const newValue = updateFieldValue(value, { ...filter.field[key] });
                const updatedFilter = {
                    ...filter,
                    container: { ...filter.container, active: false },
                    field: {
                        ...filter.field,
                        [key]: { 
                            ...newValue,
                            tempValue: value
                        }
                    },
                };
                setFilter(offFilter(updatedFilter));
                submit(updatedFilter);
                return;
            }

            setFilter(prevFilter => ({
                ...prevFilter,
                field: {
                    ...prevFilter.field,
                    email: {
                        ...prevFilter.field.email,
                        tempValue: value
                    }
                }
            }));
            return;
        }

        if (key === "subject") {
            setSubjectInput(value);
            
            if (!value || value === subjectInput) {
                const newValue = updateFieldValue(value, { ...filter.field[key] });
                const updatedFilter = {
                    ...filter,
                    container: { ...filter.container, active: false },
                    field: {
                        ...filter.field,
                        [key]: { 
                            ...newValue,
                            tempValue: value
                        }
                    },
                };
                setFilter(offFilter(updatedFilter));
                submit(updatedFilter);
                return;
            }

            setFilter(prevFilter => ({
                ...prevFilter,
                field: {
                    ...prevFilter.field,
                    subject: {
                        ...prevFilter.field.subject,
                        tempValue: value
                    }
                }
            }));
            return;
        }

        const newValue = updateFieldValue(value, { ...filter.field[key] });
        const reload = forceReload || !(newValue.value.length === 0 && value !== "");
        
        const updatedFilter = {
            ...filter,
            container: { ...filter.container, active: false},
            field: {
                ...filter.field,
                ...Object.keys(filter.field).reduce((acc, fieldKey) => ({
                    ...acc,
                    [fieldKey]: {
                        ...filter.field[fieldKey],
                        active: false
                    }
                }), {}),
                [key]: { ...newValue, active: false },
            },
        };

        setFilter(updatedFilter);

        if (reload) {
            toast.info("Reloading Data, Please Wait...");
            submit(updatedFilter);
        }
    };

    return (
        <div className="flex items-center relative" ref={containerRef}>
            <ActiveFilter
                field={field}
                handleFieldUpdateValue={handleFieldUpdateValue}
            />
            <div>
                <DisplayFilter
                    handleDisplayToggle={handleDisplayToggle}
                    container={filter.container}
                    field={filter.field}
                />

                <TextFilter
                    fieldProp={field.email}
                    fieldKey="email"
                    placeholder="Search email"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                    offFilter={() => {
                        const currentValue = field.email.tempValue || field.email.value[0] || "";
                        handleFieldUpdateValue("email", currentValue);
                        setFilter(offFilter(filter));
                    }}
                />
                <TextFilter
                    fieldProp={field.subject}
                    fieldKey="subject"
                    placeholder="Search email"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                    offFilter={() => {
                        const currentValue = field.subject.tempValue || field.subject.value[0] || "";
                        handleFieldUpdateValue("subject", currentValue);
                        setFilter(offFilter(filter));
                    }}
                />
                <OptionFilter
                    fieldProp={field.date}
                    fieldKey="date"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
                <OptionFilter
                    fieldProp={field.emailType}
                    fieldKey="emailType"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
            </div>
        </div>
    );
}
