import FormInput from "@/Components/Notification/GeneralNotification/FormInput";

export default function ScheduleSection({ 
    schedule, 
    setSchedule, 
    time, 
    setTime, 
    date, 
    setDate,
    errors = {},
    data,
    setData
}) {
    return (
        <div className="grid grid-cols-3 gap-4 mb-6">
            <div className="space-y-2">
                <label className="block font-medium">Schedule</label>
                <select
                    value={schedule}
                    onChange={(e) => setSchedule(e.target.value)}
                    className={`w-full px-4 py-2 border rounded-md ${errors.schedule_type ? 'border-red-500' : ''}`}
                >
                    <option value="one-time">One Time</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                    <option value="yearly">Yearly</option>
                </select>
                {errors.schedule_type && (
                    <p className="text-sm text-red-600">{errors.schedule_type}</p>
                )}

                {schedule === 'yearly' && data.month && (
                    <div className="mt-4">
                        <label className="block font-medium">Day of Month</label>
                        <FormInput
                            type="number"
                            value={data.day_of_month || ''}
                            onChange={(e) => {
                                const value = parseInt(e.target.value);
                                if (value >= 1 && value <= 31) {
                                    setData('day_of_month', value.toString());
                                }
                            }}
                            min="1"
                            max="31"
                            placeholder="Enter day (1-31)"
                            error={errors.day_of_month}
                            required
                        />
                    </div>
                )}
            </div>

            {schedule === 'yearly' && (
                <div className="space-y-2">
                    <label className="block font-medium">Month</label>
                    <select
                        value={data.month || ''}
                        onChange={(e) => setData('month', e.target.value)}
                        className={`w-full px-4 py-2 border rounded-md ${errors.month ? 'border-red-500' : ''}`}
                        required
                    >
                        <option value="">Select month</option>
                        <option value="1">January</option>
                        <option value="2">February</option>
                        <option value="3">March</option>
                        <option value="4">April</option>
                        <option value="5">May</option>
                        <option value="6">June</option>
                        <option value="7">July</option>
                        <option value="8">August</option>
                        <option value="9">September</option>
                        <option value="10">October</option>
                        <option value="11">November</option>
                        <option value="12">December</option>
                    </select>
                    {errors.month && (
                        <p className="text-sm text-red-600">{errors.month}</p>
                    )}
                </div>
            )}

            <div className="space-y-2">
                <label className="block font-medium">Time</label>
                <input
                    type="time"
                    value={time}
                    onChange={(e) => setTime(e.target.value)}
                    className={`w-full px-4 py-2 border rounded-md ${errors?.time ? 'border-red-500' : ''}`}
                />
                {errors?.time && (
                    <p className="text-sm text-red-600">{errors.time}</p>
                )}
            </div>

            {schedule === 'monthly' && (
                <div>
                    <label className="block font-medium">Day of the Month</label>
                    <FormInput
                        type="number"
                        value={date}
                        onChange={(e) => {
                            const value = parseInt(e.target.value);
                            if (value >= 1 && value <= 31) {
                                setDate(value.toString());
                            }
                        }}
                        min="1"
                        max="31"
                        placeholder="Enter day (1-31)"
                        error={errors.day_of_month}
                        required
                    />
                </div>
            )}

            {schedule === 'one-time' && (
                <div>
                    <label className="block font-medium">Date</label>
                    <FormInput
                        type="date"
                        value={date}
                        onChange={(date) => setDate(date)}
                        error={errors?.start_date}
                        minDate={new Date()}
                        placeholder="Select date"
                        className={errors?.start_date ? 'border-red-500' : ''}
                    />
                    
                </div>
            )}

            <div className="space-y-2 col-span-3">
                {schedule === 'weekly' && (
                    <>
                        <label className="block font-medium">Days of the Week</label>
                        <div className="flex space-x-4">
                            {['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map((day, index) => (
                                <label key={index} className="flex items-center">
                                    <input
                                        type="checkbox"
                                        value={index}
                                        checked={data.weekday && data.weekday.includes(index.toString())}
                                        onChange={(e) => {
                                            const selectedDays = [...(data.weekday || [])];
                                            if (e.target.checked) {
                                                selectedDays.push(index.toString());
                                            } else {
                                                const dayIndex = selectedDays.indexOf(index.toString());
                                                if (dayIndex > -1) {
                                                    selectedDays.splice(dayIndex, 1);
                                                }
                                            }
                                            setData('weekday', selectedDays);
                                        }}
                                        className={`mr-2 ${errors.weekday ? 'border-red-500' : ''}`}
                                    />
                                    {day}
                                </label>
                            ))}
                        </div>
                        {errors.weekday && (
                            <p className="text-sm text-red-600">{errors.weekday}</p>
                        )}
                    </>
                )}
            </div>
        </div>
    );
} 