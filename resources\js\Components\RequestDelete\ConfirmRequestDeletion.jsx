import { Dialog, Transition } from "@headlessui/react";
import { Fragment, useState } from "react";
import { router } from "@inertiajs/react";
import { toast } from "react-toastify";
import setDefaultDateFormat from "@/Util/setDefaultDateFormat";
import SecondaryButton from "@/Components/SecondaryButton";
import PrimaryButton from "@/Components/PrimaryButton";

export default function ConfirmDomainDeletionModal({ isOpen, onClose, deletionRequest }) {
    const [submitting, setSubmitting] = useState(false);
    const [inputValue, setInputValue] = useState("");
    const [isValid, setIsValid] = useState(false);
    const [showConfirmStep, setShowConfirmStep] = useState(false);

    const handleInputChange = (e) => {
        const value = e.target.value;
        setInputValue(value);
        setIsValid(value.trim().toLowerCase() === deletionRequest.email.toLowerCase());
    };

    const handleSubmit = () => {
        setShowConfirmStep(true);
    };

    const handleConfirm = () => {
        if (!isValid) return;

        setSubmitting(true);

        axios.post(
            route("domain.delete-request.approve"),
            {
                domainName: deletionRequest.domainName,
                userEmail: deletionRequest.email,
                domainId: deletionRequest.domain_id,
                createdDate: deletionRequest.created_at,
                userId: deletionRequest.user_id,
                email: inputValue,
            },
        )
            .then((response) => {
                toast.success("Domain deletion finalized successfully.");
                onClose();
                setSubmitting(false);
                router.visit(route("domain.delete-request.view"));
                return response;
            })
            .catch((error) => {
                setShowConfirmStep(false);
                setSubmitting(false);
                console.log(error.response);
                return error.response;
            });
        // router.post(
        //     route("domain.delete-request.approve"),
        //      {
        //         domainDeletion: deletionRequest,
        //         support_note: note,
        //         email: inputValue,
        //     },
        //     {
        //         onSuccess: () => {
        //             toast.success("Domain deletion finalized successfully.");
        //         },
        //         onError: (errors) => {
        //             toast.error(
        //                 "Failed to delete the domain. Please try again."
        //             );
        //             console.error(errors);
        //         },
        //     }
        // );
    };

    const resetState = () => {
        setInputValue("");
        setIsValid(false);
        setShowConfirmStep(false);
        setSubmitting(false);
        onClose();
    };

    return (
        <Transition appear show={isOpen} as={Fragment}>
            <Dialog as="div" className="relative z-50" onClose={resetState}>
                <Transition.Child
                    as={Fragment}
                    enter="ease-out duration-300"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="ease-in duration-200"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                >
                    <div className="fixed inset-0 bg-black bg-opacity-30" />
                </Transition.Child>

                <div className="fixed inset-0 overflow-y-auto">
                    <div className="flex min-h-full items-center justify-center p-4 text-center">
                        <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-300"
                            enterFrom="opacity-0 scale-95"
                            enterTo="opacity-100 scale-100"
                            leave="ease-in duration-200"
                            leaveFrom="opacity-100 scale-100"
                            leaveTo="opacity-0 scale-95"
                        >
                            <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-lg bg-white p-6 text-left align-middle shadow-xl transition-all">
                                <Dialog.Title
                                    as="h3"
                                    className="text-lg font-semibold leading-6 text-gray-900 mb-4"
                                >
                                    Confirm Domain Deletion
                                </Dialog.Title>

                                {deletionRequest.support_note && (
                                    <div className="border border-gray-200 bg-gray-50 rounded-md p-4 mt-4">
                                        <p className="text-gray-700 font-medium mb-2">Support Feedback:</p>
                                        <p className="text-sm text-gray-600"><strong>Support Agent:</strong> {deletionRequest.support_agent_name}</p>
                                        <p className="text-sm text-gray-600"><strong>Feedback Date:</strong> {deletionRequest.feedback_date}</p>
                                        <p className="text-sm text-gray-600"><strong>Note:</strong> {deletionRequest.support_note}</p>
                                    </div>
                                )}

                                {!deletionRequest.support_note?.trim() && (
                                    <>
                                        {!showConfirmStep ? (
                                            <>
                                                <div className="bg-white border border-gray-200 rounded-md p-4 mb-6">
                                                    <div className="mb-4">
                                                        <h4 className="text-lg font-medium text-gray-900 mb-2">{deletionRequest.domainName}</h4>
                                                    </div>

                                                    <div className="space-y-3">
                                                        <div className="grid grid-cols-2 gap-4">
                                                            <div>
                                                                <p className="text-sm font-medium text-gray-500">Client Name</p>
                                                                <p className="text-sm text-gray-900">{deletionRequest.first_name} {deletionRequest.last_name}</p>
                                                            </div>
                                                            <div>
                                                                <p className="text-sm font-medium text-gray-500">Client Email</p>
                                                                <p className="text-sm text-gray-900">{deletionRequest.email}</p>
                                                            </div>
                                                        </div>

                                                        <div>
                                                            <p className="text-sm font-medium text-gray-500">Request Date</p>
                                                            <p className="text-sm text-gray-900">
                                                                {setDefaultDateFormat(deletionRequest.requested_at)} {new Date(deletionRequest.requested_at + "Z").toLocaleTimeString()}
                                                            </p>
                                                        </div>

                                                        <div>
                                                            <p className="text-sm font-medium text-gray-500">Reason for Deletion</p>
                                                            <p className="text-sm text-gray-900 break-words">{deletionRequest.reason}</p>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div className="flex justify-end gap-3">
                                                    <SecondaryButton
                                                        onClick={resetState}
                                                    >
                                                        Cancel
                                                    </SecondaryButton>
                                                    <PrimaryButton
                                                        onClick={handleSubmit}
                                                        disabled={submitting}
                                                        processing={submitting}
                                                    >
                                                        {submitting ? "Processing..." : "Approve Deletion"}
                                                    </PrimaryButton>
                                                </div>
                                            </>
                                        ) : (
                                            <div className="bg-white border border-gray-200 rounded-md p-4">
                                                <p className="text-gray-900 font-medium mb-4">
                                                    Please type <strong className="text-red-600">{deletionRequest.email}</strong> to confirm deletion.
                                                </p>
                                                <input
                                                    type="text"
                                                    value={inputValue}
                                                    onChange={handleInputChange}
                                                    placeholder="Enter email address"
                                                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                                />
                                                <div className="flex justify-end gap-3 mt-4">
                                                    <SecondaryButton
                                                        onClick={() => setShowConfirmStep(false)}
                                                    >
                                                        Back
                                                    </SecondaryButton>
                                                    <PrimaryButton
                                                        onClick={handleConfirm}
                                                        disabled={!isValid || submitting}
                                                        processing={submitting}
                                                    >
                                                        {submitting ? "Processing..." : "Confirm Deletion"}
                                                    </PrimaryButton>
                                                </div>
                                            </div>
                                        )}
                                    </>
                                )}
                            </Dialog.Panel>
                        </Transition.Child>
                    </div>
                </div>
            </Dialog>
        </Transition>
    );
}
