//* PACKAGES
import React, {useState, useEffect} from 'react'

//* ICONS
//...

//* COMPONENTS
import AppButtonComponent from '@/Components/App/AppButtonComponent';
import Modal from '@/Components/Modal'; 

//* PARTIALS
//...

//* STATE
//...

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function UserManagementPermissionsReviewModalComponent(
    {
        //! PROPS
        selectedPermissions,

        //! STATES 
        stateIsModalOpen,
        
        //! EVENTS
        handleEventModalClose = () => alert('close'),
        handleEventModalConfirm = () => alert('confirm')
    }
)
{
    //! PACKAGE
    //... 

    const [currentTab,setCurrentTab] = useState('all');

    //! VARIABLES
    const getClass = (action) => {
        switch (action) {
            case 'added':
                return 'font-medium text-sm text-green-700'
            case 'removed':
                return 'font-medium text-sm text-red-600'
        }
    }

    const getIcon = (action) => {
        switch (action) {
            case 'added':
                return <span className={getClass(action)}>(+)</span>
            case 'removed':
                return <span className={getClass(action)}>(-)</span>
            default:
                break;
        }
    }

    const getFiltered = (action) => {
        switch(action){
            case 'removed':
            case 'added':
                return selectedPermissions.filter(permission => permission.action == action)
            default:
                return selectedPermissions
        }
    }

    const handleChangeTab = (tab) => {
        if(getFiltered(tab).length < 1){
            return;
        }
        setCurrentTab(tab);
    }
    //...

    //! STATES
    //...

    //! FUNCTIONS
    function handleSelfClose()
    {
        handleEventModalClose(); 
    }

    function handleConfirm()
    {
        handleEventModalConfirm();     
    }

    return (
        <Modal
            show={stateIsModalOpen}
            onClose={handleSelfClose}
            closeable={false}
            maxWidth='4xl'
        >
            <div
                className={`
                    flex flex-col justify-around
                    px-10 py-5
                    gap-y-2
                `}
            >
                {/* SECTION HEADER  */}
                <section

                >
                    <div className='text-primary text-2xl mb-10 font-medium'>
                        Review Permissions | {selectedPermissions.length - getFiltered('removed').length} Selected 
                    </div>
                    <div className='flex items-center flex-start cursor-pointer border-b text-default'>
                        <div
                            className={`text-lg px-3 ${currentTab == 'all' ? 'border-b-2 border-[#147ea7]' : ''}`}
                            onClick={() => handleChangeTab('all')}
                        >
                            <span className='text-lg text-primary'>({selectedPermissions.length - getFiltered('removed').length})</span> 
                            &nbsp;All Permissions
                        </div>

                        {getFiltered('added').length > 0 ? (
                            <div
                                className={`text-lg px-8 ${currentTab == 'added' ? 'border-b-2 border-green-600 ' : ''}`}
                                onClick={() => handleChangeTab('added')}
                                
                            >
                                <span className='text-lg text-green-600'>(+{getFiltered('added').length})</span>
                                &nbsp;Added
                            </div>
                        ) : (
                            <div
                                className={`text-lg px-8 text-gray-300 ${currentTab == 'added' ? 'border-b-2 border-green-600 ' : ''}`}
                                onClick={() => handleChangeTab('added')}
                                
                            >
                                <span className='text-lg'>(+{getFiltered('added').length})</span>
                                &nbsp;Added
                            </div>
                        )}
                        
                        {getFiltered('removed').length > 0 ? (
                            <div
                                className={`text-lg px-8 ${currentTab == 'removed' ? 'border-b-2 border-red-500 ' : ''}`}
                                onClick={() => handleChangeTab('removed')}
                            >
                                <span className='text-lg text-red-500'>(-{getFiltered('removed').length})</span>
                                &nbsp;Removed
                            </div>
                        ) : (
                            <div
                                className={`text-lg text-gray-300 px-8 ${currentTab == 'removed' ? 'border-b-2 border-red-500 ' : ''}`}
                                onClick={() => handleChangeTab('removed')}
                            >
                                <span className='text-lg'>(-{getFiltered('removed').length})</span>
                                &nbsp;Removed
                            </div>
                        )}
                        
                    </div>
                </section>

                {/* SECTION BODY */}
                <section
                    className='flex flex-col gap-y-6 max-h-96 overflow-auto'
                >
                    <aside
                        className='flex gap-4'
                    >
                    {
                        selectedPermissions.length == 0
                            ?
                                'No Permissions Found' 
                            :
                                <div
                                    className='grid grid-cols-2 gap-4 w-full px-4 pt-4'
                                >
                                    {
                                        getFiltered(currentTab)
                                            .map(
                                                (item, index) => 
                                                {
                                                    const prevItem = index > 0 ? getFiltered(currentTab)[index-1] : null;
                                                    if ((prevItem && item.category !== prevItem.category) || index == 0){
                                                        return (
                                                            <>
                                                                {index > 1 && 
                                                                    <hr className='col-span-2'/>
                                                                }
                                                                <div
                                                                    key={`category.${index}`}
                                                                    className='font-semibold text-2xl text-gray-700 col-span-2'
                                                                >
                                                                    {item.category}:
                                                                </div>
                                                                
                                                                <div
                                                                    key={index}
                                                                    className='font-medium text-sm'
                                                                >
                                                                    {getIcon(item.action)} {item.name}
                                                                </div>
                                                            </>
                                                        )
                                                    }

                                                    return (
                                                        <div
                                                            key={index}
                                                            className='font-medium text-sm'
                                                        >
                                                            {getIcon(item.action)} {item.name}
                                                        </div>
                                                    ); 
                                                }
                                            )
                                    }
                                </div>
                        } 
                    </aside>
                </section>

                <section
                    className='flex justify-end gap-x-5'
                >
                    <AppButtonComponent
                        type='button'
                        className='flex items-center gap-4  bg-primary text-white rounded-md px-4 py-2'
                        handleEventClick={handleSelfClose}
                    >
                        Close
                    </AppButtonComponent>
                    <AppButtonComponent
                        type='button'
                        className='flex items-center gap-4  bg-primary text-white rounded-md px-4 py-2'
                        handleEventClick={handleConfirm}
                    >
                        Confirm
                    </AppButtonComponent>
                </section>
            </div>
        </Modal>
    );
}
