//* PACKAGES
import React, {useState, useEffect} from 'react'
import { Link, router, useForm } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';
import "react-toastify/dist/ReactToastify.css";

//* ICONS
import { MdKeyboardBackspace } from "react-icons/md";

//* COMPONENTS
import InputError from "@/Components/InputError";
import PrimaryButton from "@/Components/PrimaryButton";
import SecondaryLink from "@/Components/SecondaryLink";
import TextInput from "@/Components/TextInput";
import TextArea from "../TextArea";
import ToggleSwitch from "../ToggleSwitch";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
import { getEventValue } from "@/Util/TargetInputEvent";

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function EditWireTransferForm(
    {
        item,
        action = "verify"
    }
)
{
    //! PACKAGE
    //...
    
    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    //...

    //! STATES
    const [netAmount, setNetAmount] = useState(item.net_amount ?? 0);

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    const { data, setData, patch, processing, errors } = useForm({
        service_fee: item.service_fee ?? 0,
        net_amount: item.net_amount ?? 0,
        note: item.note ?? "",
        is_verified: true,
        bank_transfer_id: item.id,
        payment_service_id: item.payment_service_id,
    });

    const currencyFormatter = new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
    });

    const toDollar = (number) => {
        const floatValue = parseFloat(number);
        if (isNaN(floatValue)) return 0;

        return currencyFormatter.format(floatValue.toFixed(2));
    };

    const onHandleChange = (event) => {
        const updated = getEventValue(event);
        const target = event.target.name;

        setData(target, updated);
    };

    const calculateServiceFee = () => {
        if(netAmount == 0) return toDollar(0);

        const net_amount = parseFloat(  netAmount);
        const grossAmount = parseFloat(item.gross_amount);
        const service_fee = grossAmount - net_amount;

        return toDollar(service_fee);
    };

    const validateNetAmount = (isApprove) =>
    {
        let net_amount = parseFloat(data.net_amount.trim());
        let gross_amount = parseFloat(item.gross_amount);

        if(isApprove && ((net_amount == 0) || isNaN(net_amount))) {
            toast.error("Net Amount Cannot Be Zero.");
            return false;
        }

        if(net_amount > gross_amount) {
            toast.error("Net Amount Cannot Be Greater Than Gross Amount.");
            // console.log(gross_amount);
            return false;
        }

        let threshold = gross_amount * 0.2;

        if (net_amount < threshold)
        {
            toast.error("Net Amount Cannot Be Less Than 20% of Gross Amount.");
            return false;
        }

        return true;

    }

    const handleApprove = (e) =>
    {
        e.preventDefault();

        const valid = validateNetAmount(true);

        if(!valid) return false;
        
        const formData = {
            net_amount: data.net_amount,
            note: data.note,
            is_verified: true,
            bank_transfer_id: item.id,
            payment_service_id: item.payment_service_id
        };

        patch(
            route("billing.wire.transfer.verify-update"),
            {
            data: formData,
                onSuccess: () =>
                {
                    toast.success("Wire Transfer Approved Successfully.");
                },
                onError: (errors) =>
                {
                    toast.error("Unable to Approve Wire Transfer. Please Contact Support.");
                }
            }
        );
    };

    const handleReject = (e) => {
        e.preventDefault();
        
        const formData =
        {
            net_amount        : data.net_amount,
            note              : data.note,
            is_verified       : false,
            bank_transfer_id  : item.id,
            payment_service_id: item.payment_service_id
        };

    
        patch(
            route("billing.wire.transfer.reject-update"),
            {
                data: formData,
                onSuccess: () =>
                {
                    toast.success("Wire Transfer Rejected Successfully.");
                },
                onError: (errors) =>
                {
                    toast.error("Unable to Reject Wire Transfer. Please Try Again.");
                }
            }
        );
    };

    useEffect(() => {
        // console.log('Bank Transfer Item:', item);
    }, [item]);

    const getStatus = () => {
        if (item.deleted_at) {
            return 'Unverified';
        } else if (!item.verified_at && !item.deleted_at) {
            return 'Pending';
        }
    };


    return (
        <div className="mx-auto container max-w-[500px] mt-4">
            <div className="bg-white p-4">
                <div className="flex items-center gap-2 mb-6">
                    <a href="#" onClick={() => window.history.back()}>
                        <MdKeyboardBackspace className="text-2xl" />
                    </a>
                    <h1 className="text-xl">
                        {getStatus()} Approval
                    </h1>
                </div>

                <form onSubmit={handleApprove} className="flex flex-col space-y-4">
                    <div className="flex items-center">
                        <label className="text-gray-600 text-sm w-32">Reference Number:</label>
                        <div className=''>{item.reference_number}</div>
                    </div>

                    <div className="flex items-center">
                        <label className="text-gray-600 text-sm w-32">Client:</label>
                        <div>{item.email}</div>
                    </div>

                    <div className="flex items-center">
                        <label className="text-gray-600 text-sm w-32">Name:</label>
                        <div>{item.account_name}</div>
                    </div>

                    <div className="flex items-center">
                        <label className="text-gray-600 text-sm w-32">Company:</label>
                        <div>{item.company}</div>
                    </div>

                    <div className="flex items-center">
                        <label className="text-gray-600 text-sm w-32">Sent Amount:</label>
                        <div>{toDollar(item.gross_amount)}</div>
                    </div>

                    <div className="flex items-center">
                        <label className="text-gray-600 text-sm w-32">Service Fee:</label>
                        <div>{calculateServiceFee()}</div>
                    </div>

                    <div className="flex items-center">
                        <label className="text-gray-600 text-sm w-32">Received Amount:</label>
                        <div className="flex-1">
                            <TextInput
                                type="number"
                                name="net_amount"
                                min="0"
                                step="0.01"
                                placeholder="$ 0.00"
                                className="w-full border rounded px-3 py-2"
                                value={netAmount}
                                autoComplete="net_amount"
                                handleChange={(e) => {
                                    onHandleChange(e);
                                    setNetAmount(getEventValue(e));
                                }}
                            />
                            <InputError message={errors["net_amount"]} className="mt-1" />
                        </div>
                    </div>

                    <div className="flex">
                        <label className="text-gray-600 text-sm w-32">Note:</label>
                        <div className="flex-1">
                            <TextArea
                                maxLength={1000}
                                rows={4}
                                name="note"
                                placeholder="Add notes here"
                                className="w-full border rounded px-3 py-2"
                                value={data.note}
                                autoComplete="note"
                                handleChange={onHandleChange}
                            />
                            <InputError message={errors["note"]} className="mt-1" />
                        </div>
                    </div>

                    <div className="text-center py-4">
                        <div className="text-gray-700 mb-2">Wire Transfer Amount</div>
                        <div className="text-xl font-bold">
                            {toDollar(item.gross_amount)}
                        </div>
                    </div>

                    <div className="flex justify-between gap-4 pt-2">
                        {
                            hasPermission('billing.wire.transfer.reject-update')  
                                ?
                                    <button
                                        type="button"
                                        className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50"
                                        onClick={handleReject}
                                    >
                                        Reject
                                    </button>
                                : 
                                    null
                        }
                        {
                            hasPermission('billing.wire.transfer.verify-update')  
                                ?
                                    <PrimaryButton 
                                        type="submit"
                                        processing={processing}
                                        className="flex-1 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                                    >
                                        Approve
                                    </PrimaryButton>              
                                : 
                                    null                
                        }
                    </div>
                </form>
            </div>
        </div>
    );
}
