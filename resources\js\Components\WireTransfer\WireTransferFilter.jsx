//* PACKAGES
import React, {useState, useEffect, useRef} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';
import "react-toastify/dist/ReactToastify.css";

//* ICONS
//...

//* COMPONENTS
import ActiveFilter from "@/Components/Util/Filter/ActiveFilter";
import DisplayFilter from "@/Components/Util/Filter/DisplayFilter";
import OptionFilter from "@/Components/Util/Filter/OptionFilter";
import { offFilter, updateFieldValue } from "@/Components/Util/Filter/FilterMethod";
import TextFilter from "@/Components/Util/Filter/TextFilter";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
import useOutsideClick from "@/Util/useOutsideClick";

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function WireTransferFilter()
{
    //! PACKAGE
    const currentParams = route().params;
    const containerRef = useRef();    

    //! HOOKS
    //...

    //! STATES
    const [stateInputReferenceNumber, setStateInputReferenceNumber] = useState(currentParams.referenceNumber || "");
    const [stateInputClient, setStateInputClient]                   = useState(currentParams.client || "");
    const [stateInputName, setStateInputName]                       = useState(currentParams.name || "");
    const [stateCompanyInput, setStateCompanyInput]                 = useState(currentParams.company || "");

    //! VARIABLES
    const filterConfig =
    {
        container:
        {
            active: false,
            reload: false,
        },
        field:
        {
            orderBy:
            {
                active: false,
                value: currentParams.orderBy ? [currentParams.orderBy] : [],
                type: "option",
                items:
                [
                    "Reference Number:desc",
                    "Reference Number:asc",
                    "Client:desc", 
                    "Client:asc",
                    "Name:desc",
                    "Name:asc",
                    "Company:desc",
                    "Company:asc",
                    "Amount:desc",
                    "Amount:asc",
                    "Date Created:asc",
                    "Date Created:desc",
                    "Date Updated:asc",
                    "Date Updated:desc"
                ],
                name: "Order By",
            },
            referenceNumber:
            {
                active   : false,
                value    :  currentParams.referenceNumber ? [currentParams.referenceNumber] : [],
                type     : "text",
                name     : "Reference No.",
                tempValue: stateInputReferenceNumber,
            },
            client:
            {
                active   : false,
                value    :  currentParams.client ? [currentParams.client] : [],
                type     : "text",
                name     : "Client",
                tempValue: stateInputClient,
            },
            name:
            {
                active: false,
                value:  currentParams.name ? [currentParams.name] : [],
                type: "text",
                name: "Name",
                tempValue: stateInputName,
            },
            company:
            {
                active: false,
                value: currentParams.company ? [currentParams.company] : [],
                type: "text",
                name: "Company",
                tempValue: stateCompanyInput,
            },
            status:
            {
                active: false,
                value: currentParams.status ? [currentParams.status] : [],
                type: "option",
                items:
                [
                    "Pending",
                    "Verified",
                    "Unverified",
                    "Rejected"
                ],
                name: "Status",
            },
            
        },
    };

    const [filter, setFilter] = useState(filterConfig);
    const { field } = filter;

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    useOutsideClick(
        containerRef,
        () =>
        {
            setFilter(
                (prevFilter) =>
                {
                    const updatedFilter = offFilter(prevFilter);
                    
                    return {
                        ...updatedFilter,
                        field: Object.keys(updatedFilter.field).reduce(
                            (acc, key) => (
                                {
                                    ...acc,
                                    [key]:
                                    {
                                        ...updatedFilter.field[key],
                                        active: false,
                                    },
                                }
                            ),
                            {}
                        ),
                    };
                }
            );
        }
    );

    const handleDisplayToggle = (newObject) =>
    {
        setFilter({ ...filter, ...newObject });
    };

    const handleFieldUpdateValue = (key, value) =>
    {
        if (key === "referenceNumber")
        {
            setStateInputReferenceNumber(value);

            if (!value || value === stateInputReferenceNumber)
            {
                const newValue = updateFieldValue(value, { ...filter.field[key] });
                const updatedFilter = {
                    ...filter,
                    container: { ...filter.container, active: false },
                    field: {
                        ...filter.field,
                        [key]: {
                            ...newValue,
                            tempValue: value
                        }
                    },
                };

                setFilter(offFilter(updatedFilter));

                const payload = {
                    ...currentParams,
                    [key]: value,
                };

                console.log(payload); 

                router.get(
                    route("billing.wire.transfer"),
                    payload,
                    {
                        preserveState: true,
                        replace: true,
                    }
                );
                return;
            }

            setFilter(prevFilter => ({
                ...prevFilter,
                field: {
                    ...prevFilter.field,
                    referenceNumber: {
                        ...prevFilter.field.referenceNumber,
                        tempValue: value
                    }
                }
            }));
    
            return;
        }

        if (key === "client")
        {
            setStateInputClient(value);

            if (!value || value === stateInputClient)
            {
                const newValue = updateFieldValue(value, { ...filter.field[key] });
                const updatedFilter = {
                    ...filter,
                    container: { ...filter.container, active: false },
                    field: {
                        ...filter.field,
                        [key]: {
                            ...newValue,
                            tempValue: value
                        }
                    },
                };

                setFilter(offFilter(updatedFilter));

                const payload = {
                    ...currentParams,
                    [key]: value,
                };

                router.get(
                    route("billing.wire.transfer"),
                    payload,
                    {
                        preserveState: true,
                        replace: true,
                    }
                );
                return;
            }

            setFilter(prevFilter => ({
                ...prevFilter,
                field: {
                    ...prevFilter.field,
                    client: {
                        ...prevFilter.field.client,
                        tempValue: value
                    }
                }
            }));
    
            return;
        }
        
        if (key === "name")
        {
            setStateInputName(value);

            if (!value || value === stateInputName)
            {
                const newValue = updateFieldValue(value, { ...filter.field[key] });
                const updatedFilter = {
                    ...filter,
                    container: { ...filter.container, active: false },
                    field: {
                        ...filter.field,
                        [key]: {
                            ...newValue,
                            tempValue: value
                        }
                    },
                };
                setFilter(offFilter(updatedFilter));
                const payload = {
                    ...currentParams,
                    [key]: value,
                };
                router.get(route("billing.wire.transfer"), payload, {
                    preserveState: true,
                    replace: true,
                });
                return;
            }

            setFilter(prevFilter => ({
                ...prevFilter,
                field: {
                    ...prevFilter.field,
                    name: {
                        ...prevFilter.field.name,
                        tempValue: value
                    }
                }
            }));
    
            return;
        }

        if (key === "company") {
            
            setStateCompanyInput(value);

            if (!value || value === stateCompanyInput) {
                const newValue = updateFieldValue(value, { ...filter.field[key] });
                const updatedFilter = {
                    ...filter,
                    container: { ...filter.container, active: false },
                    field: {
                        ...filter.field,
                        [key]: {
                            ...newValue,
                            tempValue: value
                        }
                    },
                };
                setFilter(offFilter(updatedFilter));
                const payload = {
                    ...currentParams,
                    [key]: value,
                };
                router.get(route("billing.wire.transfer"), payload, {
                    preserveState: true,
                    replace: true,
                });
                return;
            }

            setFilter(prevFilter => ({
                ...prevFilter,
                field: {
                    ...prevFilter.field,
                    company: {
                        ...prevFilter.field.company,
                        tempValue: value
                    }
                }
            }));
            return;
        }


        const newValue = updateFieldValue(value, { ...filter.field[key] });
        const updatedFilter = {
            ...filter,
            container: { ...filter.container, active: false },
            field: {
                ...filter.field,
                [key]: {
                    ...newValue,
                    active: false
                }
            },
        };

        setFilter(updatedFilter);

        const payload = {
            ...currentParams,
            [key]: value,
        };

        if (updatedFilter.container.reload) {
            toast.info("Reloading Data, Please Wait...");
        }

        router.get(route("billing.wire.transfer"), payload, {
            preserveState: true,
            replace: true,
        });
    };

    return (
        <div className="flex items-center relative" ref={containerRef}>
            <ActiveFilter
                field={field}
                handleFieldUpdateValue={handleFieldUpdateValue}
            />
            <div>
                <DisplayFilter
                    handleDisplayToggle={handleDisplayToggle}
                    container={filter.container}
                    field={filter.field}
                />
                <OptionFilter
                    fieldProp={field.orderBy}
                    fieldKey="orderBy"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
                <OptionFilter
                    fieldProp={field.status}
                    fieldKey="status"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
                <TextFilter
                    fieldProp={field.referenceNumber}
                    fieldKey="referenceNumber"
                    placeholder='Search Reference No.'
                    handleFieldUpdateValue={handleFieldUpdateValue}
                    offFilter={
                        () =>
                        {
                            const currentValue = field.referenceNumber.tempValue || field.referenceNumber.value[0] || "";
                            handleFieldUpdateValue("referenceNumber", currentValue);
                            setFilter(offFilter(filter));
                        }
                    }
                />
                <TextFilter
                    fieldProp={field.client}
                    fieldKey="client"
                    placeholder='Search Client'
                    handleFieldUpdateValue={handleFieldUpdateValue}
                    offFilter={
                        () =>
                        {
                            const currentValue = field.client.tempValue || field.client.value[0] || "";
                            handleFieldUpdateValue("client", currentValue);
                            setFilter(offFilter(filter));
                        }
                    }
                />
                <TextFilter
                    fieldProp={field.name}
                    fieldKey="name"
                    placeholder='Search name'
                    handleFieldUpdateValue={handleFieldUpdateValue}
                    offFilter={
                        () =>
                        {
                            const currentValue = field.name.tempValue || field.name.value[0] || "";
                            handleFieldUpdateValue("name", currentValue);
                            setFilter(offFilter(filter));
                        }
                    }
                />
                <TextFilter
                    fieldProp={field.company}
                    fieldKey="company"
                    placeholder='Search company'
                    handleFieldUpdateValue={handleFieldUpdateValue}
                    offFilter={
                        () =>
                        {
                            const currentValue = field.company.tempValue || field.company.value[0] || "";
                            handleFieldUpdateValue("company", currentValue);
                            setFilter(offFilter(filter));
                        }
                    }
                />
            </div>
        </div>
    );
}
