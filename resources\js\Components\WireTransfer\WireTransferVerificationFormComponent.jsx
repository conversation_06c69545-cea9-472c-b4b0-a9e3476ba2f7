//* PACKAGES
import React, {useState, useEffect} from 'react'
import { Link, router, useForm } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
import { MdInfo } from 'react-icons/md';

//* COMPONENTS
import AppPromptPasswordVerificationComponent from '@/Components/App/AppPromptPasswordVerificationComponent';
import InputLabel from '../InputLabel';
import PrimaryButton from '../PrimaryButton';
import TextArea from '../TextArea';
import InputError from '../InputError';

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function WireTransferVerificationFormComponent(
    {
        payment, 
        classNames, 
        purpose, 
    }
)
{
    //! PACKAGE
    const form = useForm(
        {
            purpose   : payment.purpose,
            paidAmount: payment.amount,
            note      : '',
        }
    );    

    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    const fieldItems =
    [
        {
            label         : 'reference no',
            value         : payment.reference_number,
            classNameValue: ''
        },
        {
            label         : 'purpose',
            value         : payment.purpose,
            classNameValue: 'capitalize'

        },  
        {
            label         : 'name',
            value         : payment.account_name,
            classNameValue: ''
        }, 
        {
            label         : 'company',
            value         : payment.company,
            classNameValue: ''
        },
        {
            label         : 'amount ($)',
            value         : payment.amount,
            classNameValue: ''
        },
    ]

    //! STATES
    const [stateIsActiveModalVerification, setStateIsActiveModalVerification] = useState(false);
    const [stateVerificationAction, setStateVerificationAction]               = useState(null);

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    function handleApprove() 
    {
        form.clearErrors(); 

        form.patch(
            route('billing.wire.transfer.payment.approve', { id: payment.id }), 
            {
                onSuccess: () =>
                {
                    toast.success("Wire Transfer Approved Successfully.");
                    //window.location.href = route("billing.wire.transfer");
                },
                onError: (errors) =>
                {
                    console.error('Approval Error:', errors);
                    toast.error("Unable to Approve Bank Transfer. Please Try Again.");
                }
            }
        );
    }

    function handleReject()
    {
        form.clearErrors(); 

        form.patch(
            route('billing.wire.transfer.payment.reject', { id: payment.id }), 
            {
                onSuccess: () =>
                {
                    toast.success("Wire Transfer Rejected Successfully.");
                    //window.location.href = route("billing.wire.transfer");
                },
                onError: (errors) =>
                {
                    //console.error('Rejection Error:', errors);
                    toast.error("Unable to Reject Bank Transfer. Please Try Again.");
                }
            }
        );
    }

    function handleActionSubmission()
    {
        switch (stateVerificationAction)
        {
            case 'approve': 
                handleApprove();
                break; 
            
            case 'reject':
                handleReject();
                break; 
        }
    }

    return (
        <div
            className={`
                flex flex-col gap-4 border p-5 rounded-lg justify-between
                ${classNames}
            `}
        >
            <AppPromptPasswordVerificationComponent
                show={stateIsActiveModalVerification}
                onSubmitSuccess={handleActionSubmission}
                onClose={() => 
                    {
                        setStateVerificationAction(null);
                        setStateIsActiveModalVerification(false);
                    }
                }
            />
            
            <section
                className="flex flex-col gap-4 text-sm"
            >
                {
                    fieldItems.map(
                        (fieldItem, fieldItemIndex) => 
                        {
                            return (
                                <div
                                    key={fieldItemIndex}
                                    className="flex gap-2 flex-col sm:flex-row items-start"
                                >
                                    <div className="w-40 capitalize">
                                        {fieldItem.label}
                                    </div>
                                    <div
                                        className={`
                                            ${fieldItem.classNameValue}
                                        `}
                                    >
                                        {fieldItem.value}
                                    </div>
                                </div>
                            ); 
                        }
                    )
                }

                <div
                    className="flex flex-col gap-4"
                >
                    <InputLabel
                        forInput={'note'}
                        value={'note:'}
                        className={'capitalize'}
                    />
                    <TextArea
                        value={form.data.note}
                        maxLength={500}
                        rows={7}
                        name="note"
                        placeholder="Add notes here"
                        className="w-full border rounded px-3 py-2"
                        autoComplete="note"
                        handleChange={(e) => form.setData('note', e.target.value)}
                    />
                    <InputError message={form.errors.note}/>
                </div>
                <div
                    className='
                        flex gap-2 items-center 
                        text-xs text-primary
                        text-justify
                    '
                >
                    <MdInfo
                        className='w-8 h-8'
                    />
                    {
                        purpose == 'offer payment' 
                            ? 
                                <span>
                                    Once approved, The domains listed in this transaction will start the acquisition process and the offer will be considered as Paid. If rejected, the domains will be released from hold and the offer will be considered as Closed.
                                </span>
                            : 
                                <span>
                                    Once approved, The domains listed in this transaction will start the acquisition process. If rejected, the domains will be released from hold.
                                </span>
                    }
                </div>
            </section>

            <div
                className="flex flex-col lg:flex-row justify-between gap-4"
            >
                {
                    hasPermission('billing.wire.transfer.payment.reject')  
                        ?
                            <button
                                type="button"
                                className="flex-1 px-4 py-1 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50"
                                disabled={form.processing == true}
                                onClick={
                                    () => 
                                    {
                                        setStateIsActiveModalVerification(true); 
                                        setStateVerificationAction('reject'); 
                                    }
                                }
                            >
                                Reject
                            </button>
                        : 
                            null
                }
                {
                    hasPermission('billing.wire.transfer.payment.approve')  
                        ?
                            <PrimaryButton 
                                type="submit"
                                className="flex-1 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                                disabled={form.processing == true}
                                onClick={
                                    () => 
                                    {
                                        setStateIsActiveModalVerification(true); 
                                        setStateVerificationAction('approve'); 
                                    }
                                }
                            >
                                Approve
                            </PrimaryButton>              
                        : 
                            null                
                }
            </div>
        </div>
    );
}
