//* PACKAGES
import React, { useState, useEffect } from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';
import "react-toastify/dist/ReactToastify.css";

//* ICONS
import {
    MdOutlineFilterAlt,
    MdOutlineSettings,
    MdOutlineSortByAlpha,

} from "react-icons/md";

import {
    ImSortAlphaAsc,
    ImSortAlphaDesc,
} from 'react-icons/im'

import { RiUserAddLine } from "react-icons/ri";

//* COMPONENTS
import AdminLayout from "@/Layouts/AdminLayout";
import Item from "@/Components/Client/Item";
import Filter from "@/Components/Client/Filter";
import Checkbox from "@/Components/Checkbox";
import CursorPaginate from "@/Components/Util/CursorPaginate";
import NavLink from "@/Components/NavLink";
import SecondaryButton from "@/Components/SecondaryButton";
import LoaderSpinner from "@/Components/LoaderSpinner";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
import { getEventValue } from "@/Util/TargetInputEvent";

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function Index(
    {
        items,
        onFirstPage,
        onLastPage,
        nextPageUrl,
        previousPageUrl,
        itemCount = 0,
        total = 0,
        itemName = "item",
    }
) {
    //! PACKAGE

    //! HOOKS
    const { hasPermission } = usePermissions();

    //! VARIABLES
    const SORT_TYPE =
    {
        ID_ASC: "id:asc",
        ID_DESC: "id:desc",
        NAME_ASC: "name:asc",
        NAME_DESC: "name:desc",
        EMAIL_ASC: "Email: Asc",
        EMAIL_DESC: "Email: Desc",
    };

    const { orderby = SORT_TYPE.ID_DESC, limit = 20, status, email } = route().params ?? {};

    //! STATES
    const [selectAll, setSelectAll] = useState(false);
    const [selectedUsers, setSelectedUsers] = useState([]);
    const [selectedActive, setSelectedActive] = useState([]);
    const [selectedInactive, setSelectedInactive] = useState([]);
    const [hasSpinner, setSpinner] = useState(false);

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    const resetStates = () => {
        setSelectAll(false);
        setSelectedUsers([]);
        setSelectedActive([]);
        setSelectedInactive([]);
    };

    const handleSelectAllChange = (e) => {
        const checked = getEventValue(e);
        setSelectAll(checked);

        const user_ids = items.map((item) => item.id);

        setSelectedUsers(checked ? user_ids : []);
    };

    const handleItemCheckboxChange = (userId, itemStatus, checked) => {
        setSelectedUsers((prevSelectedUsers) => {
            return checked
                ? [...prevSelectedUsers, userId]
                : prevSelectedUsers.filter((id) => id !== userId);
        });

        if (itemStatus) {
            setSelectedActive((prevSelectedActive) => {
                return checked
                    ? [...prevSelectedActive, userId]
                    : prevSelectedActive.filter((id) => id !== userId);
            });
        } else {
            setSelectedInactive((prevSelectedInactive) => {
                return checked
                    ? [...prevSelectedInactive, userId]
                    : prevSelectedInactive.filter((id) => id !== userId);
            });
        }
    };

    const handleResponse = (status) => {
        toast.info("Updating Client Status.");
        router.patch(route("client.update-status"), {
            ids: selectedUsers,
            status: status,
        });

        resetStates();
    };

    const vipInviteReq = () => {
        router.get(route("client.invite"), {
        });
    };

    const orderToggle = (sort) => {
        let payload = { orderby: sort, limit };

        payload.orderby = sort;
        if (status) payload.status = status;
        if (email) payload.email = email;

        router.get(route("client", payload));
    }

    const checkSelectedStatus = (status) => {
        if (selectedUsers.length === 0) return true;

        let selected = [...selectedUsers];
        let count = items.filter(item => {
            if (selected.includes(item.id)) {
                return item.is_active === status;
            }
        });

        let pending = items.filter(item => {
            if (selected.includes(item.id)) {
                return !item.is_active && item.is_invited
            }
        })

        if (pending && pending.length > 0) {
            return true;
        }
        return selectedUsers.length !== count.length || count.length === 0;
    }

    const handleLimitChange = (e) => {
        router.get(route("client"), {
            ...route().params,
            limit: e.target.value,
        });
    };

    router.on("start", () => {
        setSpinner(true);
    });

    router.on("finish", () => {
        setSpinner(false);
    });

    return (
        <AdminLayout>
            <div
                className="mx-auto container max-w-[1200px] mt-20 flex flex-col space-y-4"
            >
                <div
                    className="flex items-center space-x-4 justify-end"
                >
                    {
                        hasPermission('client.invite') && hasPermission('client.invite.send')
                            ?
                            <NavLink
                                href={route("client.invite")}
                                className=' hover:text-white rounded-md h-[[35px]] gap-2 px-4 !pl-3 bg-primary text-white inline-flex items-center border border-primary'
                            >
                                <span>
                                    <RiUserAddLine className="h-5 w-5" />
                                </span>
                                <span className="!ml-1">Invite User</span>
                            </NavLink>

                            :
                            null
                    }
                    {
                        hasPermission('client.update-status')
                            ?
                            <>
                                <SecondaryButton
                                    onClick={() => handleResponse(true)}
                                    processing={checkSelectedStatus(false)}
                                >
                                    Enable
                                </SecondaryButton>
                                <SecondaryButton
                                    onClick={() => handleResponse(false)}
                                    processing={checkSelectedStatus(true)}
                                >
                                    Disable
                                </SecondaryButton>
                            </>
                            :
                            null
                    }
                </div>
                <div className="flex justify-start">
                    <label className="mr-2 text-sm pt-1 text-gray-600">
                        Show
                    </label>
                    <select
                        value={limit}
                        onChange={handleLimitChange}
                        className="border border-gray-300 rounded px-4 py-1 text-sm w-20"
                    >
                        {[20, 25, 30, 40, 50, 100].map((val) => (
                            <option key={val} value={val}>
                                {val}
                            </option>
                        ))}
                    </select>
                </div>
                <div
                    id="sample"
                    className="flex items-center space-x-2 flex-wrap min-h-[2rem]"
                >
                    <label className="flex items-center">
                        <MdOutlineFilterAlt />
                        <span className="ml-2 text-sm text-gray-600">
                            Filter:
                        </span>
                    </label>

                    <Filter />
                </div>

                <div className="relative">
                    <table className="min-w-full text-left border-spacing-y-2.5 border-separate ">
                        <thead className=" bg-gray-50 text-sm">
                            <tr>
                                <th className="flex space-x-2">
                                    <label className="flex items-center pl-2 space-x-2">
                                        {items.length === 0 ?
                                            <Checkbox
                                                disabled={true}
                                                className="pointer-events-none opacity-0"
                                            /> :
                                            <Checkbox
                                                name="select_all"
                                                value="select_all"
                                                checked={selectedUsers.length === items.length}
                                                handleChange={handleSelectAllChange}
                                            />
                                        }
                                        <span className="">User</span>
                                    </label>
                                </th>
                                <th>
                                    <span>Email</span>
                                    <button
                                        onClick={() => orderToggle(orderby === SORT_TYPE.EMAIL_ASC ? SORT_TYPE.EMAIL_DESC : SORT_TYPE.EMAIL_ASC)}
                                        disabled={items.length === 0}
                                        className="pl-1"
                                    >
                                        {orderby === SORT_TYPE.EMAIL_ASC ? <ImSortAlphaAsc /> : <ImSortAlphaDesc />}
                                    </button>
                                </th>

                                <th>
                                    <span>Last Active</span>
                                </th>
                                <th>
                                    <span>Status</span>
                                </th>
                                <th>
                                    <span>IP Address</span>
                                </th>

                                <th>
                                    <span>Domains</span>
                                </th>

                                <th>
                                    <span className="text-xl">
                                        <MdOutlineSettings />
                                    </span>
                                </th>
                            </tr>
                        </thead>
                        <tbody className="text-sm">
                            {/* <<<<<<< HEAD
                            {items.map((item, index) => (
                                <Item item={item} key={"dl-" + index} />
======= */}
                            {hasSpinner ? (
                                <tr>
                                    <td colSpan={7}>
                                        <div className="mx-auto container mt-8 flex flex-col px-28 rounded-lg"><LoaderSpinner ml='ml-96' h='h-12' w='w-12' position='absolute' /><br /><span className="relative top-9 left-72 ml-20">Loading Data...</span></div>
                                    </td>
                                </tr>
                            ) : (
                                <>
                                    {items.map((user, index) => (
                                        <Item
                                            item={user}
                                            key={"dl-" + index}
                                            isSelected={selectedUsers.includes(user.id)}
                                            onCheckboxChange={handleItemCheckboxChange}
                                        />
                                    ))}
                                </>
                            )
                            }
                        </tbody>
                    </table>
                </div>
                {hasSpinner ? " " :
                    (
                        <>
                            <CursorPaginate
                                onFirstPage={onFirstPage}
                                onLastPage={onLastPage}
                                nextPageUrl={nextPageUrl}
                                previousPageUrl={previousPageUrl}
                                itemCount={itemCount}
                                total={total}
                                itemName={itemName}
                            />
                        </>
                    )
                }
            </div>
        </AdminLayout>
    );
}
