//* PACKAGES
import React, { useState } from 'react'
import { router } from '@inertiajs/react';
import "react-toastify/dist/ReactToastify.css";

//* ICONS
import {
    MdOutlineInfo,
    MdOutlineSettings,

} from "react-icons/md";

//* COMPONENTS
import AdminLayout from "@/Layouts/AdminLayout";
import CursorPaginate from "@/Components/Util/CursorPaginate";
import LoaderSpinner from "@/Components/LoaderSpinner";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
// import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
import { getEventValue } from "@/Util/TargetInputEvent";
import User from '@/Components/Client/TransactionThreshold/User';
import TransactionCard from '@/Components/Client/UserTransaction/TransactionCard';
import { _Transactions } from '@/Constant/_Transactions';
import TransactionModal from '@/Components/Client/UserTransaction/TransactionModal';
import Filter from '@/Components/Client/UserTransaction/Filter';

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function Dashboard({ items = [] }) {
    //! PACKAGE
    //! HOOKS
    // const { hasPermission } = usePermissions();

    //! VARIABLES
    //...

    //! STATES
    const [showModal, setShowModal] = useState(false);
    const [type, setType] = useState("");
    const [modalData, setModalData] = useState([]);
    const [isLoading, setIsLoading] = useState(true);

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    const handleShowModal = (type = null) => {
        const routeName = type ? "client.transaction.view" : "client.transaction.view-total";
        setIsLoading(true);
        setShowModal(true);

        axios.get(route(routeName), {
            params: { transaction: type }
        })
            .then((response) => {
                setModalData(response.data.data);
                setType(type);
                console.log(response.data.data);
            })
            .catch((error) => {
                console.error("Failed to fetch transaction:", error);
            })
            .finally(() => {
                setIsLoading(false);
            });

        // router.get(route(routeName, { transaction: type }))
    };
    //...

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[1200px] flex flex-col space-y-6">
                {/* FILTER BAR */}
                {/* Filters Grid */}
                <Filter />

                {/* Reset button on bottom right */}
                {/* <div className="flex justify-end mt-4">
                        <button className="bg-gray-100 px-3 py-1 rounded-lg hover:bg-gray-200 text-sm">
                            Reset
                        </button>
                    </div> */}


                {/* SUB HEADER */}
                <div className="flex justify-between items-center">
                    <p className="bg-white border border-gray-200/50 border-l-4 border-l-green-600 shadow-sm hover:shadow-md rounded-r-xl p-4 space-y-4 text-sm text-gray-600">
                        Showing transaction data for <span className="font-medium">All Users</span>, across all dates.
                    </p>
                    <div className='flex justify-center items-center space-x-2'>
                        <span className="has-tooltip">
                            <span className="tooltip bg-gray-800 -translate-x-1/2 -translate-y-20 text-white text-xs rounded-md px-2 py-1 transition pointer-events-none whitespace-pre-line">

                                {/* <span className="tooltip absolute bottom-full left-1/2 -translate-x-1/2 mb-2 bg-gray-800 text-white text-xs rounded-md px-2 py-1 transition pointer-events-none whitespace-pre-line"> */}
                                Switch between viewing total transaction counts and their total monetary value to quickly assess business impact.
                            </span>
                            <MdOutlineInfo />
                        </span>
                        <span className="flex space-x-1 bg-gray-200 p-1 rounded-full">
                            <button className="px-3 py-1 rounded-full bg-white text-primary font-bold text-xs hover:bg-white">Count</button>
                            <button className="px-3 py-1 rounded-full border text-xs hover:bg-primary hover:text-white">Amount</button>
                        </span>
                    </div>
                </div>

                {/* CARDS GRID */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    <TransactionCard data={items} isTotalTransaction={true} handleShowModal={() => handleShowModal()} />

                    {items.map((transaction, index) => (
                        <TransactionCard
                            key={index}
                            type={transaction.transaction_name}
                            data={transaction}
                            handleShowModal={handleShowModal}
                        />
                    ))}
                </div>
            </div>
            <TransactionModal
                data={modalData}
                type={type}
                isModalOpen={showModal}
                isLoading={isLoading}
                closeModal={() => setShowModal(false)}
            />
        </AdminLayout>
    );
}
