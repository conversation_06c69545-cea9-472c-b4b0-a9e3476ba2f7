import AdminLayout from '@/Layouts/AdminLayout'
import React from 'react'
import { useState } from 'react';
import { PiWarningOctagonLight } from 'react-icons/pi';
import { BsThreeDotsVertical } from 'react-icons/bs';
import AuthModal from './components/AuthModal';
import { MdOutlineFilterAlt } from 'react-icons/md';
import Filter from './components/AdminManualTransferFilter';
import SearchInput from '@/Components/Util/SearchInput';
import { router } from '@inertiajs/react';
import { getEventValue } from '@/Util/TargetInputEvent';
import { useEffect } from 'react';
import MarketTable from './components/MarketTable';

export default function ManualTransfer(props) {

    const [page, setPage] = useState(1);
    const [perPage, setperPage] = useState(10);
    const [totalRows, settotalRows] = useState(props.data.length);
    const [domains, setDomains] = useState(props.data);
    const [hasSpinner, setSpinner] = useState(false);
    const [pageLimit,setPageLimit] = useState(route().params.limit || 20);

    const [name, setName] = useState('');
    const [orderId, setOrderId] = useState('');

    const [showApproveModal, setShowApproveModal] = useState(false);

    const handlePageChange = async (npage) => {
        setPage(npage)
    };

    const handlePerRowsChange = async (newPerPage, page) => {
        setperPage(newPerPage)
    };

    const showAuthModal = (domain, order_id) => {
        setName(domain);
        setOrderId(order_id);
        setShowApproveModal(true);
    }

    const getAction = (domain, order_id) => {
        return <div className='relative group' style={{ textAlign: "right" }}>
            <button>
                <BsThreeDotsVertical />
            </button>
            <div className="absolute hover:text-primary invisible group-focus-within:visible right-0 z-10 p-3 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg focus:outline-none" role="menu" aria-orientation="vertical" aria-labelledby="menu-button" tabIndex="-1">
                <div className=" flex hover:bg-[#E8F3F7] rounded-md" role="none">
                    <span className=' pt-[7px] pl-2'><PiWarningOctagonLight className='w-6 h-6' /></span>
                    <button onClick={(e) => { showAuthModal(domain, order_id); e.target.blur(); }} className=" w-full text-left font-bold block py-2 pl-2 pt-[12px] text-[12px]" role="menuitem" tabIndex="-1" id="menu-item-0">
                        Manual Transfer
                    </button>
                </div>
            </div>
        </div>
    }

    const columns = [
        {
            id: 'Order ID',
            name: 'Order ID',
            selector: row => row.order_id,
            sortable: true,
        },
        {
            id: 'Domain',
            name: 'Domain',
            selector: row => row.domain,
            cell: row => <div className='lowercase'>{row.domain}</div>,
            sortable: true,
        },
        {
            id: 'User',
            name: 'User',
            selector: row => row.name,
            cell: row => <div className='capitalize'>{`${row.first_name} ${row.last_name}`}</div>,
            sortable: true,
        },
        {
            id: 'Status',
            name: 'Status',
            selector: row => row.epp_error,
            cell: row => <div className={`${row.epp_error == 'REJECTED_SUBMITTED' ? 'text-green-500' : 'text-red-500'} capitalize font-semibold`}>{`${row.epp_error == 'REJECTED_SUBMITTED' ? 'Submitted' : 'Action Required'}`}</div>,
            sortable: true,
        },
        {
            id: 'Last Updated',
            name: 'Last Updated',
            selector: row => row.updated_at,
            sortable: true,
        },
        {
            id: "Actions",
            name: 'Actions',
            selector: row => row.domain,
            cell: row => getAction(row.domain, row.order_id),
            width: '90px',
        },
    ];

    const handleSearchChange = (searchParams) => {
        let payload = route().params;
        
        payload.search = (searchParams.search != "") ? searchParams.search : undefined;

    router.get(
            route('market_manuals'),
            payload,
            { preserveState: true }
        );
    };

    const handleLimitChange = (e) => {
        setPageLimit(parseInt(getEventValue(e)));
    }

    useEffect(() => {
        setDomains(props.data);
    },[props.data])

    return (
        <AdminLayout>
            <AuthModal show={showApproveModal} order_id={orderId} setShowApproveModal={setShowApproveModal} name={name} ></AuthModal>

            <div className='mx-auto container max-w-[1200px] mt-20 flex flex-col px-5 rounded-lg '>
                <div className='flex justify-start'>
                    <div>
                        <div className='text-3xl font-semibold mb-3'>
                            Marketplace Manual Transfer
                        </div>
                        <span className='text-gray-500 max-w-lg'>
                            View and Manage Marketplace Manual Transfer
                        </span>
                    </div>
                </div>
                <div
                    className="flex justify-start"
                    style={{ position: "relative", top: "20px"}}
                >
                    <label className="mr-2 text-sm pt-1 text-gray-600">
                        Show
                    </label>
                    <select
                        value={pageLimit}
                        onChange={handleLimitChange}
                        className="border border-gray-300 rounded px-4 py-1 text-sm w-20"
                    >
                        {[20, 25, 30, 40, 50, 100].map((val) => (
                            <option key={val} value={val}>
                                {val}
                            </option>
                        ))}
                    </select>
                </div>
                <div
                    id="sample"
                    className="flex items-center space-x-2 flex-wrap min-h-[2rem] pt-4 mt-4 justify-between"
                >
                    <div className='flex'>
                        <label className="flex items-center">
                            <MdOutlineFilterAlt />
                            <span className="ml-2 text-sm text-gray-600">
                                Filter:
                            </span>
                        </label>
                    
                        <Filter />
                    </div>
                    <SearchInput
                        onSearchChange={handleSearchChange}
                        placeholder="Search domain"
                    />
                </div>
                <MarketTable
                    hasSpinner={hasSpinner}
                    items={domains}
                    columns={columns}
                    rowsPerPage={10}
                />
            </div>
        </AdminLayout>
    )
}
