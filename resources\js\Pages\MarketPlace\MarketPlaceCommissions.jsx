import AdminLayout from '@/Layouts/AdminLayout'
import React from 'react'
import { useState } from 'react';
import { BsThreeDotsVertical } from "react-icons/bs";
import { PiWarningOctagonLight } from "react-icons/pi";
import { MdOutlineFilterAlt } from 'react-icons/md';
import Filter from './components/AdminCommissionFilter';
import SearchInput from '@/Components/Util/SearchInput';
import { router } from '@inertiajs/react';
import { getEventValue } from '@/Util/TargetInputEvent';
import { useEffect } from 'react';
import MarketTable from './components/MarketTable';
import { useRef } from 'react';

export default function MarketPlaceCommissions(props) {

    const initialMount = useRef(true);

    const [page, setPage] = useState(1);
    const [data, setData] = useState([]);
    const [search, setSearch] = useState('');
    const [domains, setDomains] = useState([]);
    const [perPage, setperPage] = useState(10);
    const [hasSpinner, setSpinner] = useState(false);
    const [totalRows, settotalRows] = useState(props.data.length);
    const [pageLimit,setPageLimit] = useState(10);

    const [tempDomains, setTempDomains] = useState(domains);

    const getStatus = (status) => {
        let color = 'bg-primary';

        if(status.toLowerCase() == 'pending') color = `bg-yellow-500`
        else if(status.toLowerCase() == 'completed') color = `bg-green-500`
        else if(status.toLowerCase() == 'cancelled') color = `bg-red-500`

        return <div className='flex'>
            <span className={`w-2 h-2 mt-1 mr-2 rounded-full ${color}`}> </span>
            <span className='capitalize'>{status}</span>
        </div>
    }

    const handlePageChange = async (npage) => {
        setPage(npage)
    };

    const handlePerRowsChange = async (newPerPage, page) => {
        setperPage(newPerPage)
    };

    const getAction = (id) => {
        return <div className='relative group' style={{ textAlign: "right" }}>
            <button>
                <BsThreeDotsVertical />
            </button>
            <div className="absolute hover:text-primary invisible group-focus-within:visible right-0 z-10 p-3 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg focus:outline-none" role="menu" aria-orientation="vertical" aria-labelledby="menu-button" tabIndex="-1">
                <div className="flex hover:bg-[#E8F3F7] rounded-md" role="none">
                    <span className='pt-[7px] pl-2'><PiWarningOctagonLight className='w-6 h-6' /></span>
                    <button onClick={(e) => { console.log(id); e.target.blur(); }} className="font-bold block py-2 pl-2 pt-[12px] text-[12px]" role="menuitem" tabIndex="-1" id="menu-item-0">Show Domain History</button>
                </div>
            </div>
        </div>
    }

    const columns = [
        {
            id: 'name',
            name: 'User',
            selector: row => row.name,
            cell: row => <div className='capitalize'>{`${row.first_name} ${row.last_name}`}</div>,
            sortable: true,
            width: '150px'
        },
        {
            id: 'domain',
            name: 'Domain',
            selector: row => row.domain,
            cell: row => <div className='lowercase'>{row.domain}</div>,
            sortable: true,
            width: '150px'
        },
        {
            id: 'status',
            name: 'Status',
            selector: row => row.status,
            cell: row => getStatus(row.status),
            sortable: true,
            width: '170px'
        },
        {
            id: 'price',
            name: 'Price',
            selector: row => parseInt(row.price),
            cell: row => `$${parseInt(row.price / 1000000)}`,
            sortable: true,
            width: '100px',
        },
        {
            id: 'commission',
            name: 'Commission',
            selector: row => parseInt(row.commission),
            cell: row => `$${parseInt(row.commission / 1000000)}`,
            sortable: true,
            width: '140px',
        },
        {
            id: 'is_audited',
            name: 'Audited',
            cell: row => <div className='capitalize'>{row.is_audited ? 'True' : 'False'}</div>,
            sortable: true,
            width: '110px',
        },
        {
            id: 'created_at',
            name: 'Date',
            cell: row => row.created_at,
            sortable: true,
            width: '180px',
        },
        {
            id: "Actions",
            name: 'Actions',
            selector: row => row.id,
            cell: row => getAction(row.id),
            width: '90px',
        },
    ];

    const handleSearchChange = ({search}) => {
        setSearch(search)

        if(search.trim().length <= 0) setTempDomains(data['data'])
        else { setTempDomains(
                data['data'].filter((a) => {
                return (a.domain).includes(search)
            }))
        }
    };

    const paginate = (page, limit) => {
        setSpinner(true)

        axios.post(route(`commissions.get`), { 
            page: page,
            count: limit,
        }).then((a) => {
            setData(a.data.data)
    
            setTempDomains(a.data.data['data'])
            setSpinner(false)
        })
    }

    const handleLimitChange = (e) => {
        paginate(1, parseInt(getEventValue(e)));
        setPageLimit(parseInt(getEventValue(e)));
    }

    useEffect(() => {
        if (initialMount.current) {
            initialMount.current = false;
            return;
        }

        paginate(page, pageLimit)
    }, [page])

    useEffect(()=>{
        setData(props.data)
        setTempDomains(props.data.data);
    }, [])

    return (
        <AdminLayout>
            <div className='mx-auto container max-w-[1200px] mt-20 flex flex-col px-5 rounded-lg '>
                <div className='flex justify-start'>
                    <div>
                        <div className='text-3xl font-semibold mb-3'>
                            Marketplace Commissions
                        </div>
                        <span className='text-gray-500 max-w-lg'>
                            View and Manage Marketplace Commissions
                        </span>
                    </div>
                </div>
                <div
                    className="flex justify-start"
                    style={{ position: "relative", top: "20px"}}
                >
                    <label className="mr-2 text-sm pt-1 text-gray-600">
                        Show
                    </label>
                    <select
                        value={pageLimit}
                        onChange={handleLimitChange}
                        className="border border-gray-300 rounded px-4 py-1 text-sm w-20"
                    >
                        {[10, 20, 25, 30, 40, 50, 100].map((val) => (
                            <option key={val} value={val}>
                                {val}
                            </option>
                        ))}
                    </select>
                </div>
                <div
                    id="sample"
                    className="flex items-center justify-between space-x-2 flex-wrap min-h-[2rem] pt-4 mt-4"
                >
                    <div className='flex'>
                        <label className="flex items-center">
                            <MdOutlineFilterAlt />
                            <span className="ml-2 text-sm text-gray-600">
                                Filter:
                            </span>
                        </label>
                        <Filter />                    
                    </div>

                    <SearchInput
                        onSearchChange={handleSearchChange}
                        placeholder="Search domain"
                    />
                </div>
                <MarketTable
                    hasSpinner={hasSpinner}
                    items={tempDomains}
                    columns={columns}
                    rowsPerPage={10}
                />
                <div className='flex justify-between'>

                    <span className='text-sm'>Showing: {data.from} - {data.to} of {data.total}</span>
                    <div className='flex gap-3'>
                        <button onClick={() => { setPage((prev) => prev - 1) }} disabled={data.prev_page_url == null} className={`border border-gray-500 p-1 px-2 rounded-lg disabled:border-gray-300 disabled:text-gray-300`}> prev </button>
                        <button onClick={() => { setPage((prev) => prev + 1) }} disabled={data.next_page_url == null} className={`border border-gray-500 p-1 px-2 rounded-lg disabled:border-gray-300 disabled:text-gray-300`}> next </button>
                    </div>
                </div>
            </div>
        </AdminLayout>
    )
}
