import React, { useState } from 'react'
import OfferHistoryPopup from '../components/OfferHistoryPopup';
import AdminLayout from '@/Layouts/AdminLayout';
import { BiEdit } from "react-icons/bi";
import { useEffect } from 'react';
import Filter from '../components/AdminMarketOfferFilter';
import { MdOutlineFilterAlt } from 'react-icons/md';
import { getEventValue } from '@/Util/TargetInputEvent';
import MarketTable from '../components/MarketTable';
import axios from 'axios';

import { useRef } from 'react';

export default function ShowOffers({myoffers}) {

    const [search, setSearch] = useState('');
    const [offers, setOffers] = useState([]);
    const [modal, showModal] = useState(false);
    const [hasSpinner, setSpinner] = useState(false);

    const [page, setPage] = useState(1);
    const [data, setData] = useState([]);
    const [maximum, setMaximum] = useState(10);
    const [pageLimit, setPageLimit] = useState(10);
    const [domain, setDomain] = useState({name: '', created_at: new Date(), offer_price: 0});

    const initialMount = useRef(true);

    const columns = [
  
        { name: "Reference ID", id: "reference_id", sortable: true, alpha: true, width: '140px', cell: row => <span className='py-5'>{row.reference_id}</span> },
        { name: "Domain", id: "domain_name", sortable: true, alpha: true, width: '200px', cell: row => row.domain_name },
        { name: "Initial Offer", id: "offer_price", sortable: true, alpha: false, width: '130px', cell: row => `$${row.offer_price}` },
        { name: "Last Status Update", id: "updated_at", sortable: true, alpha: false, width: '180px', cell: row => `${new Date(row.updated_at).toLocaleString()}` },
        { name: "Status", id: "offer_status", sortable: true, alpha: true, width: '190px', cell: row => getStatus(row.offer_status) },
        { name: "Buy Now Price", id: "counter_offer_price", sortable: true, alpha: false, width: '150px', cell: row => `${row.counter_offer_price > 0 ? `$${row.counter_offer_price}` : 'NA'}`, },
        { name: "Action", id: "action", sortable: false, cell: row => getDetailButton(row) },
    ];

    const handlePopUp = (row) => {
  
        setDomain(row)
        showModal(true);
    }

    const getDetailButton = (row) => {
        return <div className='flex gap-1 font-bold'>
            <div className='has-tooltip'>
                <span className='tooltip rounded shadow-lg bg-gray-100 text-green-700 px-3 py-1 -mt-8'>Edit</span>
                <button onClick={() => { handlePopUp(row) }} className='bg-green-500 bg-opacity-20 rounded-md font-bold text-lg text-green-700 p-1.5 flex items-center space-x-2'>
                    <BiEdit className=' font-bold' />
                </button>
            </div>

        </div>
    }

    const statusColors = {
        waiting: "bg-gray-500 text-white",
        offer_closed: "bg-red-500 text-white",
        counter_offer: "bg-yellow-500 text-black",
        offer_accepted: "bg-green-500 text-white",
        offer_rejected: "bg-pink-500 text-white",
        user_counter_offer: "bg-orange-500 text-white",

        paid_hold_pending: "bg-blue-500 text-white",
        paid_order_pending: "bg-purple-500 text-white",
        paid_transfer_pending: "bg-teal-500 text-white",
        paid_transfer_requested: "bg-indigo-500 text-white",
        paid_transfer_completed: "bg-lime-500 text-black",
    };

    const getStatus = (status) => {
        return <span className={`${statusColors[status]} p-1 px-3 capitalize text-xs rounded-full`}>{status.replaceAll('_', ' ').replaceAll('offer', '')}</span>
    }

    const doSearch = (search) => {
        setSearch(search)

        if(search.trim().length <= 0) setOffers(myoffers['data'])
        else {
            setOffers(myoffers['data'].filter((a) => {
                return ((a.domain_name).includes(search) || (a.reference_id ? (a.reference_id).includes(search) : false))
            }))
        }
    }

    const getDiv = (name, price, status) => {
        return <div className='flex flex-col py-3'>
            <div className='text-xl font-medium text-gray-800'>{name}</div>
            <div className='text-[15px] font-medium text-gray-500 flex gap-2 pt-1'>
                <div className='pt-0.5'>${price}</div>
  
    
                    {getStatus(status)}
            </div>
        </div>
    }

    const handleLimitChange = (e) => {
        setPageLimit(parseInt(getEventValue(e)))
        paginate(1, parseInt(getEventValue(e)));
    }

    const paginate = (page, limit) => {
        setSpinner(true)

        axios.post(route(`offers.get`), { 
            page: page,
            count: limit,
        }).then((a) => {
            setData(a.data.myoffers)
            setMaximum(a.data.myoffers.total)
  
    
            setOffers(a.data.myoffers['data'])
            setSpinner(false)
        })
    }

    useEffect(() => {
        if (initialMount.current) {
            initialMount.current = false; // skip the first render
            return;
        }

        paginate(page, pageLimit)
    }, [page])

    useEffect(() => {
        setOffers(myoffers.data);

        const urlParams = new URLSearchParams(window.location.search);
        
        const domain = urlParams.get("domain"); // "stuff" or null if not present

        return doSearch(domain ? `${domain.replaceAll('\'', '').replaceAll('?', '')}` : "")
    }, [myoffers])

    useEffect(() => {
        setData(myoffers)
        setMaximum(myoffers.total)
    }, [])

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[1200px] mt-20 flex flex-col space-y-4">
                <OfferHistoryPopup
                    showModal={showModal}
                    offers={offers}
                    setOffers={setOffers}
                    getStatus={getStatus}
                    modal={modal}
                    domain={domain}
                />

                <div className='flex justify-start ml-5'>
                    <div>
                        <div className='text-3xl font-semibold mb-3'>
                            Marketplace Offers
                        </div>
                        <span className='text-gray-500 max-w-lg'>
                            View and Manage Marketplace Offers
                        </span>
                    </div>
                </div>

                <div
                    className="flex justify-start ml-5"
                    style={{ top: "20px"}}
                >
      
          
                        <label className="mr-2 text-sm pt-1 text-gray-600">
                        Sho  
                        w
                    </label>
                    <select
                        value={pageLimit}
                        onChange={(e) => {handleLimitChange(e)}}
      
        
                        className="border border-gray-300 rounded px-4 py-1 text-sm w-20"
                    >
    
                          {[10, 20, 25, 30, 40, 50, 100].map((val) => (
                            <option key={val} value={val}>
                                {val}
                            </option>
                        ))}
                    </select>
                </div>
                
                <div className="mt-1 ml-5">
                    <div className='flex flex-row mb-5 justify-between'>
                        <div className='flex'>
                            <label className="flex items-center">
                                <MdOutlineFilterAlt />
                                    <span className="ml-2 text-sm text-gray-600">
                                        Filter:
                                    </span>
                            </label>
                                        
                            <Filter />
                        </div>
                        <div className="flex w-72 gap-3 ml-4">
                            <label htmlFor="search" className="pt-2">
                                Search:{" "}
                            </label>
                            <input
                                value={search}
                                onChange={(e) => {
                                    doSearch(e.target.value);
                                }}
                                id="search"
                                type="text"
                                className="max-w-xs w-full border border-gray-300 rounded-md"
                                placeholder="reference id or domain"
                            />
                        </div>
  
    
                        </div>
                    <MarketTable
                        hasSpinner={hasSpinner}
                        items={offers}
                        columns={columns}
                        rowsPerPage={10}
                    />
                </div>
                <div className='flex justify-between'>
  
                      <span className='text-sm'>Showing: {data.from} - {data.to} of {maximum}</span>
                    <div className='flex gap-3'>
                        <button onClick={() => { setPage((prev) => prev - 1) }} disabled={data.prev_page_url == null} className={`border border-gray-500 p-1 px-2 rounded-lg disabled:border-gray-300 disabled:text-gray-300`}> prev </button>
                        <button onClick={() => { setPage((prev) => prev + 1) }} disabled={data.next_page_url == null} className={`border border-gray-500 p-1 px-2 rounded-lg disabled:border-gray-300 disabled:text-gray-300`}> next </button>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
