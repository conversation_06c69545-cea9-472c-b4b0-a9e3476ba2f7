import React from "react";
import ActiveFilter from "@/Components/Util/Filter/ActiveFilter";
import DisplayFilter from "@/Components/Util/Filter/DisplayFilter";
import OptionFilter from "@/Components/Util/Filter/OptionFilter";
import TextFilter from "@/Components/Util/Filter/TextFilter";
import { useRef, useState } from "react";
import {
    offFilter,
    updateFieldValue,
} from "@/Components/Util/Filter/FilterMethod";
import { router } from "@inertiajs/react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import useOutsideClick from "@/Util/useOutsideClick";

export default function Filter() {
    const { orderby, domain, status, audited } = route().params;
    const containerRef = useRef();
    
    // Add state for email input
    const [domainInput, setDomainInput] = useState(domain || "");
    

    const config = {
        container: {
            active: false
        },

        field: {
            orderby: {
                active: false,
                value: orderby ? [orderby] : [],
                type: "option",
                items: [
                    "User: Asc",
                    "User: Desc",
                    "Initial Offer: Asc",
                    "Initial Offer: Desc",
                    "Last Updated: Asc",
                    "Last Updated: Desc",
                    "Buy Now Price: Asc",
                    "Buy Now Price: Desc",
                ],
                name: "Order By",
            },
            domain: {
                active: false,
                value: domainInput ? [domainInput] : [],
                type: "text",
                name: "Domain",
                // Add temporary value for display during typing
                tempValue: domainInput,
            },
            status: {
                active: false,
                value: status ? [status] : [],
                type: "option",
                items: [
                    "Waiting",
                    "Counter Offer",
                    "Offer Accepted",
                    "Offer Rejected",
                    "Offer Closed",
                    "Paid",
                    "Paid Hold Pending",
                    "Paid Order Pending",
                    "Paid Transfer Pending",
                    "Paid Transfer Requested",
                    "Paid Transfer Completed",
                    "User Counter",
                ],
                name: "Status",
            }
        },
    };

    const [filter, setFilter] = useState(config);
    const { field } = filter;

    useOutsideClick(containerRef, () => {
        setFilter(prevFilter => {
            const updatedFilter = offFilter(prevFilter);
            return {
                ...updatedFilter,
                field: Object.keys(updatedFilter.field).reduce((acc, key) => ({
                    ...acc,
                    [key]: {
                        ...updatedFilter.field[key],
                        active: false
                    }
                }), {})
            };
        });
    });

    const submit = (updatedFilter) => {
        const { orderby, domain, status } = updatedFilter.field;
        let payload = route().params;

        if (domain.value.length > 0 || domain.tempValue == '') payload.domain = domain.value[0];
        payload.orderby = (orderby.value.length > 0) ? orderby.value[0] : undefined;
        payload.status = (status.value.length > 0) ? status.value[0]: undefined;

        router.get(route('offers'), payload, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleDisplayToggle = (newObject) => {
        setFilter({ ...filter, ...newObject });
    };

    const handleFieldUpdateValue = (key, value, forceReload = false) => {
        if (key === "domain") {
            setDomainInput(value);
            
            if (!value || value === domainInput) {
                const newValue = updateFieldValue(value, { ...filter.field[key] });
                const updatedFilter = {
                    ...filter,
                    container: { ...filter.container, active: false },
                    field: {
                        ...filter.field,
                        [key]: { 
                            ...newValue,
                            tempValue: value
                        }
                    },
                };
                setFilter(offFilter(updatedFilter));
                submit(updatedFilter);
                return;
            }

            setFilter(prevFilter => ({
                ...prevFilter,
                field: {
                    ...prevFilter.field,
                    domain: {
                        ...prevFilter.field.domain,
                        tempValue: value
                    }
                }
            }));
            return;
        }

        const newValue = updateFieldValue(value, { ...filter.field[key] });
        const reload = forceReload || !(newValue.value.length === 0 && value !== "");
        
        const updatedFilter = {
            ...filter,
            container: { ...filter.container, active: false},
            field: {
                ...filter.field,
                ...Object.keys(filter.field).reduce((acc, fieldKey) => ({
                    ...acc,
                    [fieldKey]: {
                        ...filter.field[fieldKey],
                        active: false
                    }
                }), {}),
                [key]: { ...newValue, active: false },
            },
        };

        setFilter(updatedFilter);

        if (reload) {
            toast.info("Reloading Data, Please Wait...");
            submit(updatedFilter);
        }
    };

    return (
        <div className="flex items-center relative" ref={containerRef}>
            <ActiveFilter
                field={field}
                handleFieldUpdateValue={handleFieldUpdateValue}
            />
            <div>
                <DisplayFilter
                    handleDisplayToggle={handleDisplayToggle}
                    container={filter.container}
                    field={filter.field}
                />

                <TextFilter
                    fieldProp={field.domain}
                    fieldKey="domain"
                    placeholder="Search domain"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                    offFilter={() => {
                        const currentValue = field.domain.tempValue || field.domain.value[0] || "";
                        handleFieldUpdateValue("domain", currentValue);
                        setFilter(offFilter(filter));
                    }}
                />
                <OptionFilter
                    fieldProp={field.orderby}
                    fieldKey="orderby"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
                <OptionFilter
                    fieldProp={field.status}
                    fieldKey="status"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
            </div>
        </div>
    );
}
