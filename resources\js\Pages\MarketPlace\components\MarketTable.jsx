  
  import { useState, useMemo } from "react";
import LoaderSpinner from "@/Components/LoaderSpinner";
import { ImSortAlphaAsc, ImSortAlphaDesc } from "react-icons/im";
import { TbSortAscending2, TbSortDescending2 } from "react-icons/tb";

export default function MarketTable({ hasSpinner, items, columns }) {
  
  const [sortConfig, setSortConfig] = useState({ field: null, direction: "asc" });

  // Sort the items (client-side)
  const sortedItems = useMemo(() => {
    if (!sortConfig.field) return items;
  
    return [...items].sort((a, b) => {
      const aValue = a[sortConfig.field];
      const bValue = b[sortConfig.field];

      if (typeof aValue === "number" && typeof bValue === "number") {
        return sortConfig.direction === "asc" ? aValue - bValue : bValue - aValue;
      } else {
        return sortConfig.direction === "asc"
  
      
        ? String(aValue).localeCompare(String(bValue))
          : String(bValue).localeCompare(String(aValue));
      }
    });
  }, [items, sortConfig]);

  const handleSort = (field) => {
    setSortConfig((prev) => {
      if (prev.field === field) {
      

        return { field, direction: prev.direction === "asc" ? "desc" : "asc" };
      }
      return { field, direction: "asc" };
    });
  };

  return (
    <>
      <table className="min-w-full text-left border-spacing-y-2.5 border-separate text-sm">
  
          <thead className="bg-gray-50">
          <tr>
            {columns.map((col) => (
              <th key={col.id}>
                <div
                  className="flex items-center space-x-2 cursor-pointer select-none"
                  onClick={() => col.sortable && handleSort(col.id)}
                >
        
                    <span>{col.name}</span>
                  {col.sortable && sortConfig.field === col.id && (
        
        <>
                      {col.alpha ? (
                        sortConfig.direction === "asc" ? (
                          <ImSortAlphaAsc />
                        ) : (
  
    
      
    
                          <ImSortAlphaDesc />
                        )
                      ) : (
                        sortConfig.direction === "asc" ? (
                          <TbSortAscending2 />
                        ) : (
                          <TbSortDescending2 />
                        )
                      )}
                    </>
                  )}
                </div>
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {hasSpinner ? (
            <tr>
              <td colSpan={columns.length}>
      
                  <LoaderSpinner />
              </td>
            </tr>
          ) : sortedItems.length === 0 ? (
            <tr>
              <td colSpan={columns.length} className="text-center py-4">
                No data
              </td>
            </tr>
          ) : (
            sortedItems.map((item, i) => (
              <tr key={i} className="border-b">
                {columns.map((col) => (
                    <td key={col.id} className="" style={{ width: col.width }}>
                      {col.cell ? col.cell(item) : item[col.id]}
                    </td>
                ))}
              </tr>
            ))
          )}
        </tbody>
      </table>
    </>
  );
}
