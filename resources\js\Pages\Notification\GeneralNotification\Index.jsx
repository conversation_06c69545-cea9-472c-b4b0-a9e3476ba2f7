import AdminLayout from "@/Layouts/AdminLayout";
import PrimaryButton from "@/Components/PrimaryButton";
import { router, useForm } from '@inertiajs/react';
import ScheduleSection from "@/Components/Notification/GeneralNotification/ScheduleSection";
import FormInput from "@/Components/Notification/GeneralNotification/FormInput";
import UserSelection from "@/Components/Notification/GeneralNotification/UserSelection";
import { toast } from "react-toastify";
import { useState } from 'react';

const INITIAL_FORM_STATE = {
    selected_users: [],
    selection_mode: 'select',
    title: '',
    message: '',
    link_name: '',
    redirect_url: '',
    type: 'Important',
    schedule_type: 'one-time',
    time: '',
    start_date: '',
    weekday: [],
    day_of_month: '',
    month: '',
    min_registration_period: '',
    max_registration_period: '',
    expiration: ''
};

export default function Index({ users }) {
    const [showForm, setShowForm] = useState(false);
    const { data, setData, post, processing, errors } = useForm(INITIAL_FORM_STATE);
    const [excludedUsers, setExcludedUsers] = useState(new Set());
    const [selectedEmails, setSelectedEmails] = useState([]);
    const [validationErrors, setValidationErrors] = useState({});

    const resetForm = () => {
        setData(INITIAL_FORM_STATE);
        setShowForm(false);
        setSelectedEmails([]);
        setExcludedUsers(new Set());
        setValidationErrors({});
    };

    const handleUserSelectionChange = (emails, mode) => {
        let finalEmails;
        if (mode === 'all') {
            finalEmails = users
                .filter(user => !excludedUsers.has(user.id))
                .map(user => user.email);
        } else {
            finalEmails = emails;
        }

        setSelectedEmails(finalEmails);
        setData(prevData => ({
            ...prevData,
            selection_mode: mode,
            preview_emails: finalEmails,
            selected_users: users
                .filter(user => finalEmails.includes(user.email))
                .map(user => user.id)
        }));
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        
        // Prepare the data with preview_emails
        const formData = {
            ...data,
            preview_emails: data.selection_mode === 'all'
                ? users
                    .filter(user => !excludedUsers.has(user.id))
                    .map(user => user.email)
                : selectedEmails,
            selected_users: data.selection_mode === 'select' 
                ? users
                    .filter(user => selectedEmails.includes(user.email))
                    .map(user => user.id)
                : []
        };
        
        router.post(route('notification.general.store'), formData, {
            preserveScroll: true,
            preserveState: true,
            onSuccess: () => {
                toast.success('Notification scheduled successfully.');
                resetForm();
            },
            onError: (error) => {
                const validationErrors = {
                    errors: {
                        title: error.title,
                        message: error.message,
                        link_name: error.link_name,
                        redirect_url: error.redirect_url,
                        time: error.time,
                        start_date: error.start_date,
                        weekday: error.weekday,
                        day_of_month: error.day_of_month,
                        month: error.month,
                        selected_users: error.selected_users
                    }
                };
                
                setValidationErrors(validationErrors);
                toast.error('Failed to schedule notification. Please check the form.');
                
                // Keep the form visible
                setShowForm(true);
                
                // Preserve all form data and selections
                if (data.selection_mode === 'select') {
                    // For select mode, preserve the selected emails and form data
                    setData(prevData => ({
                        ...prevData,
                        preview_emails: selectedEmails,
                        selected_users: users
                            .filter(user => selectedEmails.includes(user.email))
                            .map(user => user.id)
                    }));
                } else {
                    // For all mode, preserve the excluded users and form data
                    setData(prevData => ({
                        ...prevData,
                        preview_emails: users
                            .filter(user => !excludedUsers.has(user.id))
                            .map(user => user.email),
                        selected_users: users
                            .filter(user => !excludedUsers.has(user.id))
                            .map(user => user.id)
                    }));
                }
            }
        });
    };

    const handleModeChange = (mode) => {
        setData('selection_mode', mode);
        if (mode === 'all') {
            setSelectedEmails([]);
        }
    };

    const handleSetSchedule = () => {
        setShowForm(true);
        setTimeout(() => {
            document.getElementById('schedule-section')?.scrollIntoView({ behavior: 'smooth' });
        }, 100);
    };

    const shouldShowScheduleButton = !showForm && 
        ((data.selection_mode === 'all') || 
         (data.selection_mode === 'select' && data.preview_emails?.length > 0));

    return (
        <AdminLayout>
            <form onSubmit={handleSubmit} className="mx-auto container max-w-[1100px] mt-8">
                <div className="mx-auto container max-w-[1100px] mt-8 flex flex-col space-y-6">
                    <div className="flex items-center justify-between">
                        <div className="flex flex-col space-y-2">
                            {/* <h2 className="text-4xl font-semibold">
                                General Notifications
                            </h2> */}
                            
                        </div>
                    </div>
                    <div className="bg-white rounded-lg space-y-6">
                        <div>
                            <div className="flex items-center justify-between mb-4">
                                <div className="flex items-center space-x-4">
                                    {['all', 'select'].map(mode => (
                                        <div key={mode} className="flex items-center space-x-2">
                                            <input
                                                type="radio"
                                                id={mode}
                                                checked={data.selection_mode === mode}
                                                onChange={() => {
                                                    setShowForm(false);
                                                    handleUserSelectionChange([], mode);
                                                    handleModeChange(mode);
                                                }}
                                                className="w-4 h-4 text-blue-600"
                                            />
                                            <label htmlFor={mode} className="capitalize">{mode}</label>
                                        </div>
                                    ))}
                                </div>
                                <button
                                    type="button"
                                    onClick={resetForm}
                                    className="text-blue-600 hover:text-blue-800 text-sm"
                                >
                                    Reset
                                </button>
                            </div>

                            <UserSelection
                                users={users}
                                onSelectionChange={handleUserSelectionChange}
                                showSelectionInterface={true}
                                selectionMode={data.selection_mode}
                                selectedUsers={data.selected_users}
                                selectedEmails={selectedEmails}
                                setSelectedEmails={setSelectedEmails}
                                excludedUsers={excludedUsers}
                                setExcludedUsers={setExcludedUsers}
                            />

                            <div className="mt-6">
                                <h3 className="font-medium mb-3">Selected</h3>
                                <div className="h-[200px] overflow-y-auto border rounded-lg p-4">
                                    <div className="grid grid-cols-3 gap-2">
                                        {data.preview_emails ? (
                                            data.preview_emails.map((email, index) => (
                                                <div key={index} className="text-sm text-gray-600">
                                                    {email}
                                                </div>
                                            ))
                                        ) : (
                                            <div className="text-sm text-gray-500">
                                                No users selected
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>

                            {!showForm && shouldShowScheduleButton && (
                                <div className="flex justify-end mt-4">
                                    <PrimaryButton
                                        onClick={handleSetSchedule}
                                        type="button"
                                    >
                                        Set the Schedule
                                    </PrimaryButton>
                                </div>
                            )}
                        </div>

                        {showForm && (
                            data.selection_mode === 'all' || 
                            (data.selection_mode === 'select' && data.preview_emails?.length > 0)
                        ) && (
                            <>
                                <div id="schedule-section" className="container mx-auto">
                                    <ScheduleSection
                                        schedule={data.schedule_type}
                                        setSchedule={(value) => setData('schedule_type', value)}
                                        time={data.time}
                                        setTime={(value) => setData('time', value)}
                                        date={data.schedule_type === 'one-time' 
                                            ? data.start_date 
                                            : data.schedule_type === 'weekly' 
                                                ? data.weekday.join(', ')
                                                : data.day_of_month}
                                        setDate={(value) => {
                                            if (data.schedule_type === 'one-time') {
                                                setData('start_date', value);
                                            } else if (data.schedule_type === 'weekly') {
                                                setData('weekday', value);
                                            } else if (data.schedule_type === 'monthly') {
                                                setData('day_of_month', value);
                                            }
                                        }}
                                        errors={validationErrors?.errors}
                                        data={data}
                                        setData={setData}
                                    />
                                    <div className="mb-6">
                                        <div className="grid grid-cols-3 gap-4">
                                            <FormInput
                                                label="Min Registration Period (Optional)"
                                                type="number"
                                                value={data.min_registration_period}
                                                onChange={(e) => {
                                                    const value = e.target.value;
                                                    setData('min_registration_period', value === '' ? null : parseInt(value));
                                                }}
                                                placeholder="e.g., 1 Day"
                                                error={validationErrors?.errors?.min_registration_period}
                                                className="[appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                                            />

                                            <FormInput
                                                label="Max Registration Period (Optional)"
                                                type="number"
                                                value={data.max_registration_period}
                                                onChange={(e) => {
                                                    const value = e.target.value;
                                                    setData('max_registration_period', value === '' ? null : parseInt(value));
                                                }}
                                                placeholder="e.g., 365 Days"
                                                error={validationErrors?.errors?.max_registration_period}
                                                className="[appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                                            />

                                            {data.schedule_type !== 'one-time' && (
                                                <FormInput
                                                    label="Expiration (Optional)"
                                                    type="date"
                                                    value={data.expiration}
                                                    onChange={(date) => setData('expiration', date)}
                                                    error={validationErrors?.errors?.expiration}
                                                    placeholder="Select date"
                                                    minDate={new Date()}
                                                />
                                            )}
                                        </div>
                                    </div>
                                </div>

                                <div className="grid grid-cols-3 gap-4">
                                    <div className="space-y-2">
                                        <label className="block font-medium">Type</label>
                                        <select 
                                            value={data.type}
                                            onChange={(e) => setData('type', e.target.value)}
                                            className={`w-full px-4 py-2 border rounded-md ${validationErrors?.errors?.type ? 'border-red-500' : ''}`}
                                        >
                                            <option value="Important">Important</option>
                                            <option value="Normal">Normal</option>
                                        </select>
                                        {validationErrors?.errors?.type && (
                                            <p className="text-sm text-red-600">{validationErrors.errors.type}</p>
                                        )}
                                    </div>

                                    <FormInput
                                        label="Link Name"
                                        type="text"
                                        value={data.link_name}
                                        onChange={(e) => setData('link_name', e.target.value)}
                                        error={validationErrors?.errors?.link_name}
                                        placeholder="Enter link name"
                                    />

                                    <FormInput
                                        label="URL"
                                        type="text"
                                        value={data.redirect_url}
                                        onChange={(e) => setData('redirect_url', e.target.value)}
                                        error={validationErrors?.errors?.redirect_url}
                                        placeholder="Enter URL"
                                    />
                                </div>

                                <FormInput
                                    label="Title"
                                    type="text"
                                    value={data.title}
                                    onChange={(e) => setData('title', e.target.value)}
                                    error={validationErrors?.errors?.title}
                                    placeholder="Enter title"
                                />

                                <div className="space-y-2">
                                    <label className="block font-medium">Message</label>
                                    <textarea
                                        value={data.message}
                                        onChange={(e) => setData('message', e.target.value)}
                                        className={`w-full px-4 py-2 border rounded-md h-32 ${
                                            validationErrors?.errors?.message ? 'border-red-500' : ''
                                        }`}
                                    />
                                    {validationErrors?.errors?.message && (
                                        <p className="text-sm text-red-600">{validationErrors.errors.message}</p>
                                    )}
                                </div>

                                <div className="flex justify-end">
                                    <PrimaryButton 
                                        type="submit"
                                        disabled={processing}
                                    >
                                        {processing ? 'Posting...' : 'Post'}
                                    </PrimaryButton>
                                </div>
                            </>
                        )}
                    </div>
                </div>
            </form>
        </AdminLayout>
    );
}
