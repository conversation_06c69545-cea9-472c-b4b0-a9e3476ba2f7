//* PACKAGES
import React, {useState, useEffect} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
import { FiSettings, FiFilter } from "react-icons/fi";
import { MdOutlineFilterAlt, MdOutlineSettings } from "react-icons/md";

//* COMPONENTS
import AdminLayout from "@/Layouts/AdminLayout";
import CursorPaginate from "@/Components/Util/CursorPaginate";
import Checkbox from "@/Components/Checkbox";
import DeleteConfirmationModal from "@/Components/Notification/DeleteConfirmationModal";
import Filter from "@/Components/Notification/Filter";
import LoaderSpinner from "@/Components/LoaderSpinner";
import NotificationManagementItem from "@/Components/Notification/Item";
import PrimaryButton from "@/Components/PrimaryButton";
import SecondaryButton from "@/Components/SecondaryButton";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';
import { capitalize } from 'lodash';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

const NotificationManagement = (
    {
        notifications,
        filters
    }
) =>
{
     //! PACKAGE
    //...
    
    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    //...

    //! STATES
    const [selectedItems, setSelectedItems] = useState(new Set());
    const [isTableLoading, setTableLoading] = useState(false);
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [itemsToDelete, setItemsToDelete] = useState([]);
    const [showFilterMenu, setShowFilterMenu] = useState(false);
    const [previousNotifications, setPreviousNotifications] = useState(null);

    const [limit,setLimit] = useState(route().params.limit ?? 20);

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    const handleSelectAll = (isSelected) => {
        setSelectedItems(new Set(isSelected ? notifications.data.map(n => n.id) : []));
    };
    
    const handleSelect = (id, isSelected) => {
        setSelectedItems(prev => {
            const newSet = new Set(prev);
            isSelected ? newSet.add(id) : newSet.delete(id);
            return newSet;
        });
    };

    const areAllSelected = notifications.data.length > 0 && 
        notifications.data.every(notification => selectedItems.has(notification.id));

    const hasExpiredSelected = () => {
        return Array.from(selectedItems).some(id => {
            const notification = notifications.data.find(n => n.id === id);
            return notification?.status === 'expired';
        });
    };

    const hasActiveSelected = () => {
        return Array.from(selectedItems).some(id => {
            const notification = notifications.data.find(n => n.id === id);
            return notification?.status === 'active' || notification?.status === 'pending';
        })
    }

    const hasDisabledSelected = () => {
        return Array.from(selectedItems).some(id => {
            const notification = notifications.data.find(n => n.id === id);
            return notification?.status === 'disabled';
        })
    }

    const shouldDisableActions = () => {
        return selectedItems.size === 0 || hasExpiredSelected();
    };

    const getActionTooltip = () => {
        if (selectedItems.size === 0) return 'Select Items to Perform Actions.';
        if (hasExpiredSelected()) return 'Cannot Update Expired Notifications.';
        return '';
    };

    const handleStatusUpdate = (ids, status) => {
        if (Array.isArray(ids) && shouldDisableActions()) {
            toast.error('Cannot Update Expired Notifications.');
            return;
        }

        setTableLoading(true);
        router.post(route('notification.management.update-status'), {
            ids: Array.isArray(ids) ? ids : [ids],
            status: status
        }, {
            preserveScroll: true,
            onSuccess: () => {
                setSelectedItems(new Set());
                setTableLoading(false);
                toast.success(`Notification${ids.length > 1 ? 's' : ''} ${status === 'pending' ? 'Enabled' : 'Disabled'} Successfully.`);
            },
            onError: (errors) => {
                setTableLoading(false);
                toast.error(errors.ids || `Failed to Update Notification${ids.length > 1 ? 's' : ''}.`);
            }
        });
    };

    const handleEnable = () => {
        if (selectedItems.size === 0) return;
        handleStatusUpdate(Array.from(selectedItems), 'pending');
    };

    const handleDisable = () => {
        if (selectedItems.size === 0) return;
        handleStatusUpdate(Array.from(selectedItems), 'disabled');
    };

    const searchFieldChange = (name, value) => {
        const params = new URLSearchParams(filters);
        if (value && value !== '') {
            params.set(name, value);
        } else {
            params.delete(name);
        }
        setTableLoading(true);
        router.get(route('notification.management'), Object.fromEntries(params));
    };

    const handleBulkDelete = () => {
        if (selectedItems.size === 0) return;
        setItemsToDelete(Array.from(selectedItems));
        setShowDeleteModal(true);
    };
    router.on("start", () => {
        setTableLoading(true);
    });

    router.on("finish", () => {
        setTableLoading(false);
    });

    const actions =
    [
        {
            type            : 'primary',
            hasAccess       : hasPermission('notification.general'),
            label           : 'add notification',
            isDisabled      : false,
            title           : null, 
            isProcessing    : false, 
            handleEventClick: () => router.visit(route('notification.general')),
        }, 
        {
            type            : 'secondary',
            hasAccess       : hasPermission('notification.management.update-status'),
            label           : 'enable',
            isDisabled      : shouldDisableActions && hasActiveSelected,
            title           : getActionTooltip, 
            isProcessing    : shouldDisableActions, 
            handleEventClick: handleEnable,
        }, 
        {
            type            : 'secondary',
            hasAccess       : hasPermission('notification.management.update-status'),
            label           : 'disable',
            isDisabled      : shouldDisableActions && hasDisabledSelected,
            title           : getActionTooltip, 
            isProcessing    : shouldDisableActions, 
            handleEventClick: handleDisable,
        }, 
        {
            type            : 'secondary',
            hasAccess       : hasPermission('notification.management.delete'),
            label           : 'delete',
            isDisabled      : selectedItems.size === 0,
            title           : 'delete', 
            isProcessing    : selectedItems.size === 0, 
            handleEventClick: handleBulkDelete,
        }
    ]; 

    const handleLimitChange = (e) => {
            const newLimit = e.target.value;
            setLimit(newLimit);
    
            // Update URL with the new limit value and keep searchTerm intact
            const updatedParams = { ...route().params, limit: newLimit, page: 1 };
            router.get(route('notification.management'), updatedParams, {
                preserveState: true,
            });
        };
    
    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[1200px] mt-20 flex flex-col space-y-4">
                <div>
                    <h1 className="text-4xl font-bold">General Notification</h1>
                    <p>Essential Updates, Announcements, and Alerts for Your Convenience</p>
                </div>
                {/* Action Buttons */}

                
                <div
                    className="flex items-center space-x-4 justify-between"
                >
                    <div
                        className="flex justify-start"
                    >
                        <label className="mr-2 text-sm pt-1 text-gray-600">
                            Show
                        </label>
                        <select
                            value={limit}
                            onChange={handleLimitChange}
                            className="border border-gray-300 rounded px-4 py-1 text-sm w-20"
                        >
                            {[20, 25, 30, 40, 50, 100].map((val) => (
                                <option key={val} value={val}>
                                    {val}
                                </option>
                            ))}
                        </select>
                    </div>
                    <div className='flex space-x-4'>
                    {
                        actions.filter(action => action.hasAccess)
                            .map(
                                (action, actionIndex) =>
                                {
                                    if (action.type == 'primary')
                                    {
                                        return (
                                            <PrimaryButton
                                                key={actionIndex}
                                                className={'capitalize'}
                                                onClick={action.handleEventClick}
                                            >
                                                {action.label}
                                            </PrimaryButton>
                                        )
                                    }

                                    return (
                                        <SecondaryButton
                                            key={actionIndex}
                                            title={typeof action.title === 'function' ? action.title() : action.title}
                                            disabled={typeof action.isDisabled === 'function' ? action.isDisabled() : action.isDisabled}
                                            processing={typeof action.isProcessing === 'function' ? action.isProcessing() : action.isProcessing}
                                            onClick={action.handleEventClick}
                                        >
                                            {action.label}
                                        </SecondaryButton>
                                    )
                                }
                            )
                    }
                    </div>
                    {/* <PrimaryButton
                        onClick={() => router.visit('/notification/general')}
                    >
                        Add Notification
                    </PrimaryButton>
                    <SecondaryButton
                        onClick={handleEnable}
                        disabled={shouldDisableActions()}
                        title={getActionTooltip()}
                        processing={shouldDisableActions()}
                    >
                        Enable
                    </SecondaryButton>
                    <SecondaryButton
                        onClick={handleDisable}
                        disabled={shouldDisableActions()}
                        title={getActionTooltip()}
                        processing={shouldDisableActions()}
                    >
                        Disable
                    </SecondaryButton>
                    <SecondaryButton
                        onClick={handleBulkDelete}
                        processing={selectedItems.size === 0}
                    >
                        Delete
                    </SecondaryButton> */}
                </div>

                {/* Filter Section */}
                <div className="flex items-center space-x-2 flex-wrap min-h-[2rem]">
                    <label className="flex items-center">
                        <MdOutlineFilterAlt />
                        <span className="ml-2 text-sm text-gray-600">
                            Filter:
                        </span>
                    </label>
                    <Filter />
                </div>

                

                {/* Table */}
                <div>
                    <table className="min-w-full text-left border-separate border-spacing-y-2">
                        <thead className="bg-gray-50 text-sm">
                            <tr>
                                <th>
                                    <label className="flex items-center space-x-2">
                                        <Checkbox 
                                            checked={areAllSelected}
                                            handleChange={(e) => handleSelectAll(e.target.checked)}
                                            className="ml-0"
                                        />
                                        <span>Title</span>
                                    </label>
                                </th>
                                <th className='pl-4'>
                                    <span>Status</span>
                                </th>
                                <th >
                                    <span>Users</span>
                                </th>
                                <th>
                                    <span>Next Date</span>
                                </th>
                                <th>
                                    <span>Type</span>
                                </th>
                                <th>
                                    <span>Schedule</span>
                                </th>
                                <th className="pl-1">
                                    <span className="text-xl ">
                                        <MdOutlineSettings />
                                    </span>
                                </th>
                            </tr>
                        </thead>
                        <tbody className="text-sm">
                            {isTableLoading ? (
                                <tr>
                                    <td colSpan={7}>
                                        <div className="flex flex-col items-center justify-center py-12">
                                            <LoaderSpinner h='h-12' w='w-12' />
                                            <span className="mt-4">Loading Data...</span>
                                        </div>
                                    </td>
                                </tr>
                            ) : (
                                notifications.data.length === 0 ? (
                                    <tr>
                                        <td colSpan={7} className="text-center py-12">
                                            No notifications found
                                        </td>
                                    </tr>
                                ) : (
                                    notifications.data.map((notification) => (
                                        <NotificationManagementItem
                                            key={notification.id}
                                            notification={notification}
                                            onSelect={handleSelect}
                                            isSelected={selectedItems.has(notification.id)}
                                            onStatusUpdate={handleStatusUpdate}
                                        />
                                    ))
                                )
                            )}
                        </tbody>
                    </table>
                </div>

                {!isTableLoading && (
                    <CursorPaginate 
                        onFirstPage={notifications.current_page === 1}
                        onLastPage={notifications.current_page === notifications.last_page}
                        nextPageUrl={notifications.next_page_url}
                        previousPageUrl={notifications.prev_page_url}
                        itemCount={notifications.data.length}
                        total={notifications.total}
                        itemName="notification"
                    />
                )}

                <DeleteConfirmationModal 
                    show={showDeleteModal}
                    onClose={() => setShowDeleteModal(false)}
                    onConfirm={() => {
                        setShowDeleteModal(false);
                        setTableLoading(true);
                        router.post(route('notification.management.delete'), {
                            ids: itemsToDelete
                        }, {
                            preserveScroll: true,
                            onSuccess: () => {
                                setSelectedItems(new Set());
                                setTableLoading(false);
                                toast.success('Notification/s deleted successfully.');
                            },
                            onError: () => {
                                setTableLoading(false);
                                toast.error('Failed To Delete Notifications.');
                            }
                        });
                    }}
                    itemCount={itemsToDelete.length}
                />
            </div>
        </AdminLayout>
    );
};

export default NotificationManagement;
