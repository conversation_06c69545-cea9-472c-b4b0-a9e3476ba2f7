import TransactionItem from "@/Components/Setting/TransactionThreshold/TransactionItem";
import AdminLayout from "@/Layouts/AdminLayout";
import { useForm } from "@inertiajs/react";
import { useState } from "react";
import { MdMode, MdOutlineSettings } from "react-icons/md";
import { toast } from "react-toastify";

export default function Default({ transactions = [] }) {
    const { data, setData, patch, processing, reset, isDirty, setDefaults } = useForm(transactions);
    const [editAll, setEditAll] = useState(false);

    const handleChange = (transactionType, key, value) => {
        setData(transactionType, {
            ...data[transactionType],
            [key]: Number(value)
        });
    };

    const handleActionChanged = (transactionType, action) => {
        const actionMap = {
            "Notify & Reject": { allow_action: false, notify_subscriber: true },
            "Notify": { allow_action: true, notify_subscriber: true },
            "Reject": { allow_action: false, notify_subscriber: false },
            "None": { allow_action: true, notify_subscriber: false }
        };

        if (actionMap[action]) {
            setData(transactionType, {
                ...data[transactionType],
                ...actionMap[action]
            });
        }
    };

    const handleReset = (transactionType) => {
        reset(transactionType);
    };

    const handleSetDefaults = (transactionType) => {
        setDefaults(transactionType, data[transactionType]);
    };

    const handleEditAll = () => {
        setEditAll(true);
    };

    const handleCancelAll = () => {
        setEditAll(false);
        reset();
    };

    const handleUpdateAll = (e) => {
        e.preventDefault()

        patch(route("setting.transaction.threshold-updateAll"), {
            onSuccess: () => {
                toast.success("All transactions updated successfully");
                setDefaults();
                setEditAll(false);
            }
        });
    }

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[1100px] flex flex-col space-y-2 px-8">
                <div className="mb-4">
                    <h1 className="text-xl font-bold">System Transaction Threshold</h1>
                    <p className="text-sm text-gray-600">
                        Define the default transaction limits and automated actions for all users across the system.
                    </p>
                </div>

                <table className="min-w-full text-left border-separate ">
                    <thead className="bg-gray-50 text-sm">
                        <tr className="uppercase text-xs">
                            <th className="py-3 px-3">
                                <span className="flex items-center">Transaction Type</span>
                            </th>
                            <th className="pl-3 py-2 px-3">
                                <span>System Limit</span>
                            </th>
                            <th className="pl-3 py-2">
                                <span>User Limit</span>
                            </th>
                            <th className="pl-3 py-2 px-3">
                                <span>Days</span>
                            </th>
                            <th className="py-2 pl-3 px-3">
                                <span>Trigger Action</span>
                            </th>
                            <th className="flex justify-center items-center py-2 px-3">
                                <span className="text-xl">
                                    <MdOutlineSettings />
                                </span>
                            </th>
                        </tr>
                    </thead>
                    <tbody className="text-sm">
                        {Object.values(data).map((item, index) => (
                            <TransactionItem
                                item={item}
                                key={"tti-" + index}
                                handleChange={handleChange}
                                handleReset={handleReset}
                                handleActionChanged={handleActionChanged}
                                editAllTrigger={editAll}
                                handleSetDefaults={handleSetDefaults}
                            />
                        ))}
                    </tbody>
                </table>
                <div className="flex justify-end pr-2">
                    <span className="flex justify-end space-x-4">
                        <button
                            className={`border border-gray-300 rounded-md px-4 py-2 text-sm transition ease-in-out duration-150 ${!isDirty || processing ? "opacity-50 cursor-not-allowed" : "bg-primary border-transparent text-white hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"}`}
                            disabled={!isDirty || processing}
                            onClick={handleUpdateAll}
                        >
                            Update All
                        </button>
                        {editAll ? (
                            <button
                                className={`flex items-center border border-gray-300 rounded-md px-4 py-2 text-sm transition ease-in-out duration-150 ${processing ? "opacity-50 cursor-not-allowed text-gray-500" : "text-gray-700 hover:bg-gray-100 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"}`}
                                disabled={processing}
                                onClick={handleCancelAll}
                            >
                                Cancel All
                            </button>
                        ) : (
                            <button
                                className="inline-flex items-center text-sm space-x-1 text-gray-500 hover:text-primary"
                                onClick={handleEditAll}
                            >
                                <MdMode />
                                <span>Edit All</span>
                            </button>
                        )}
                    </span>

                </div>
            </div>
        </AdminLayout >
    );
}
