//* PACKAGES
import React, {useState, useEffect} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
//...

//* COMPONENTS
//...

//* PARTIALS
import PartialWireTransferTable from './PartialWireTransferTable';

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function PartialWireTransferSectionAddCredit(
    {
        items,
        onFirstPage,
        onLastPage,
        nextPageUrl,
        previousPageUrl,
        itemCount = 0,
        total     = 0,
        SORT_TYPE, 
        paramOrderBy, 
        paramCompany, 
        selectedItems,
        setSelectedItems, 
        stateSelectedItem,
        setStateSelectedItem,
        setStateModalActiveNote
    }
)
{
    //! PACKAGE
    //...
    
    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    const columns = 
    [
        'referenceNumber', 
        'client',
        'accountName', 
        'company', 
        'grossAmount', 
        'status',     
        'note', 
        'dateCreated', 
        //'dateUpdated', 
        'actions'
    ]

    //! STATES
    //...

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    //...

    return (
        <PartialWireTransferTable
            columns                 = {columns}
            items                   = {items}
            onFirstPage             = {onFirstPage}
            onLastPage              = {onLastPage}
            nextPageUrl             = {nextPageUrl}
            previousPageUrl         = {previousPageUrl}
            itemCount               = {itemCount}
            total                   = {total}
            SORT_TYPE               = {SORT_TYPE}
            paramOrderBy            = {paramOrderBy}
            paramCompany            = {paramCompany}
            selectedItems           = {selectedItems}
            setSelectedItems        = {setSelectedItems}
            stateSelectedItem       = {stateSelectedItem}
            setStateSelectedItem    = {setStateSelectedItem}
            setStateModalActiveNote = {setStateModalActiveNote}
        />
    );
}
