//* PACKAGES
import React, {useState, useEffect} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
import { ImSortAlphaAsc, ImSortAlphaDesc, ImSortNumericAsc , ImSortNumbericDesc   } from "react-icons/im";
import { MdOutlineSettings, MdOutlineFilterAlt } from "react-icons/md";
import { TbSortAscending2, TbSortDescending2 } from "react-icons/tb";
import { FaSort, FaSortNumericUp, FaSortNumericDown} from "react-icons/fa";

//* COMPONENTS
import CursorPaginate from "@/Components/Util/CursorPaginate";
import WireTransferItemComponent from "@/Components/WireTransfer/WireTransferItemComponent";
import LoaderSpinner from "@/Components/LoaderSpinner";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function PartialWireTransferTable(
    {
        columns = [], 
        items,
        onFirstPage,
        onLastPage,
        nextPageUrl,
        previousPageUrl,
        itemCount = 0,
        total     = 0,
        SORT_TYPE, 
        setStateSelectedItem,
        setStateModalActiveNote
    }
)
{
    //! PACKAGE
    const params = 
    {
        orderBy : route().params.orderBy,
        company : route().params.company,
        purpose : route().params.purpose,
    }
    
    console.log(onLastPage); 
    
    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    //...

    //! STATES
    const [stateIsPageLoading, setStateIsPageLoading] = useState(false); 

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    function handleSortOrder(sortOrder)
    {
        let payload = {};

        payload.orderBy = sortOrder;
        payload.purpose = params.purpose; 

        router.get(
            route("billing.wire.transfer"),
            payload, 
            {
                replace : true
            }
        );
    };

    function displaySortIcon(valueSortAsc, valueSortDesc, iconType='alpha')
    {
        let icon = <FaSort/>

        if (params.orderBy == valueSortAsc)
        {
            switch (iconType)
            {
                case "alpha":
                    icon = <ImSortAlphaAsc />
                    break; 
                case "numeric":
                    icon = <ImSortNumericAsc />
                    break;  
                case "date":
                    icon = <TbSortAscending2 />
                    break; 
            }
        
        }
        
        if (params.orderBy == valueSortDesc)
        {
            switch (iconType)
            {
                case "alpha":
                    icon = <ImSortAlphaDesc />
                    break; 
                case "numeric":
                    icon = <ImSortNumbericDesc />
                    break;  
                case "date":
                    icon = <TbSortDescending2 />
                    break; 
            }
        }

        return icon; 
    }

    router.on(
        "start",
        () =>
        {
            setStateIsPageLoading(true);
        }
    );

    router.on(
        "finish",
        () =>
        {
            setStateIsPageLoading(false);
        }
    );

    return (
        <div
            className='flex flex-col gap-4'
        >                
            <table
                className="min-w-[1200px] text-left border-spacing-y-2.5 border-separate"
            >
                <thead
                    className="bg-gray-50 text-sm"
                >
                    <tr>
                        <th
                            className={`
                                ${columns.includes('referenceNumber') == true ? '' : 'hidden'}
                                py-3
                            `}
                        >
                            <label
                                className="flex items-center space-x-2"
                            >
                                <span>
                                    Reference No.
                                </span>
                                <button
                                    onClick={() =>
                                        handleSortOrder(
                                            params.orderBy ===
                                                SORT_TYPE.REFERENCE_NUMBER_ASC
                                                ? SORT_TYPE.REFERENCE_NUMBER_DESC
                                                : SORT_TYPE.REFERENCE_NUMBER_ASC
                                        )
                                    }
                                    disabled={items.length === 0}
                                >
                                    {
                                        displaySortIcon(
                                            SORT_TYPE.REFERENCE_NUMBER_ASC,
                                            SORT_TYPE.REFERENCE_NUMBER_DESC
                                        )
                                    }
                                </button>
                            </label>
                        </th>
                        <th
                            className={`
                                ${columns.includes('client') == true ? '' : 'hidden'}
                            `}
                        >
                            <label
                                className="flex items-center space-x-2"
                            >
                                <span>
                                    Client
                                </span>
                                <button
                                    onClick={() =>
                                        handleSortOrder(
                                            params.orderBy ===
                                                SORT_TYPE.CLIENT_ASC
                                                ? SORT_TYPE.CLIENT_DESC
                                                : SORT_TYPE.CLIENT_ASC
                                        )
                                    }
                                    disabled={items.length === 0}
                                >
                                    {
                                        displaySortIcon(
                                            SORT_TYPE.CLIENT_ASC,
                                            SORT_TYPE.CLIENT_DESC
                                        )
                                    }
                                </button>
                            </label>
                        </th>
                        <th
                            className={`
                                ${columns.includes('accountName') == true ? '' : 'hidden'}
                            `}
                        >
                            <label
                                className="flex items-center space-x-2"
                            >
                                <span>Name</span>
                                <button
                                    onClick={() =>
                                        handleSortOrder(
                                            params.orderBy ===
                                                SORT_TYPE.NAME_ASC
                                                ? SORT_TYPE.NAME_DESC
                                                : SORT_TYPE.NAME_ASC
                                        )
                                    }
                                    disabled={items.length === 0}
                                >
                                    {
                                        displaySortIcon(
                                            SORT_TYPE.NAME_ASC,
                                            SORT_TYPE.NAME_DESC
                                        )
                                    }
                                </button>
                            </label>
                        </th>
                        <th
                            className={`
                                ${columns.includes('company') == true ? '' : 'hidden'}
                            `}
                        >
                            <label
                                className="flex items-center space-x-2"
                            >
                                <span>Company</span>
                                <button
                                    onClick={() =>
                                        handleSortOrder(
                                            params.orderBy ===
                                                SORT_TYPE.COMPANY_ASC
                                                ? SORT_TYPE.COMPANY_DESC
                                                : SORT_TYPE.COMPANY_ASC
                                        )
                                    }
                                    disabled={items.length === 0}
                                >
                                    {
                                        displaySortIcon(
                                            SORT_TYPE.COMPANY_ASC,
                                            SORT_TYPE.COMPANY_DESC
                                        )
                                    }
                                </button>
                            </label>
                        </th>
                        <th
                            className={`
                                ${columns.includes('domains') == true ? '' : 'hidden'}
                            `}
                        >
                            Domain(s) 
                        </th>
                        <th
                            className={`
                                ${columns.includes('grossAmount') == true ? '' : 'hidden'}
                            `}
                        >
                            <label
                                className="flex items-center space-x-2"
                            >
                                <span>Amount ($)</span>
                                <button
                                    onClick={() =>
                                        handleSortOrder(
                                            params.orderBy ===
                                                SORT_TYPE.AMOUNT_ASC
                                                ? SORT_TYPE.AMOUNT_DESC
                                                : SORT_TYPE.AMOUNT_ASC
                                        )
                                    }
                                    disabled={items.length === 0}
                                >
                                    {
                                        displaySortIcon(
                                            SORT_TYPE.AMOUNT_ASC,
                                            SORT_TYPE.AMOUNT_DESC, 
                                            'numeric'
                                        )
                                    }
                                </button>
                            </label>
                        </th>
                        <th
                            className={`
                                ${columns.includes('purpose') == true ? '' : 'hidden'}
                            `}
                        >
                            Purpose
                        </th>
                        <th
                            className={`
                                ${columns.includes('status') == true ? '' : 'hidden'}
                            `}
                        >
                            Status
                        </th>
                        <th
                            className={`
                                ${columns.includes('note') == true ? '' : 'hidden'}
                            `}
                        >
                            Note
                        </th>
                        <th
                            className={`
                                ${columns.includes('dateCreated') == true ? '' : 'hidden'}
                            `}
                        >
                            <label className="flex items-center space-x-2">
                                <span>Date Created</span>
                                <button
                                    onClick={() =>
                                        handleSortOrder(
                                            params.orderBy ===
                                                SORT_TYPE.CREATED_ASC
                                                ? SORT_TYPE.CREATED_DESC
                                                : SORT_TYPE.CREATED_ASC
                                        )
                                    }
                                    disabled={items.length === 0}
                                >
                                    {
                                        displaySortIcon(
                                            SORT_TYPE.CREATED_ASC,
                                            SORT_TYPE.CREATED_DESC, 
                                            'date'
                                        )
                                    }
                                </button>
                            </label>
                        </th>
                        <th
                            className={`
                                ${columns.includes('dateUpdated') == true ? '' : 'hidden'}
                            `}
                        >
                            <label className="flex items-center space-x-2">
                                <span>Date Updated</span>
                                <button
                                    onClick={() =>
                                        handleSortOrder(
                                            params.orderBy ===
                                                SORT_TYPE.UPDATED_ASC
                                                ? SORT_TYPE.UPDATED_DESC
                                                : SORT_TYPE.UPDATED_ASC
                                        )
                                    }
                                    disabled={items.length === 0}
                                >
                                    {
                                        displaySortIcon(
                                            SORT_TYPE.UPDATED_ASC,
                                            SORT_TYPE.UPDATED_DESC, 
                                            'date'
                                        )
                                    }
                                </button>
                            </label>
                        </th>
                        <th
                            className={`
                                ${columns.includes('actions') == true ? '' : 'hidden'}
                            `}
                        >
                            <span className="text-xl">
                                <MdOutlineSettings />
                            </span>
                        </th>
                    </tr>
                </thead>
                <tbody
                    className="text-sm"
                >
                    {
                        stateIsPageLoading == true 
                            ?
                                <tr>
                                    <td
                                        colSpan={columns.length}
                                        className='py-5'
                                    >
                                        <div
                                            className="flex flex-col items-center mx-auto container gap-4 rounded-lg"
                                        >
                                            <LoaderSpinner h='h-12' w='w-12' />
                                            <span className=" text-primary duration-200 ease-linear animate-pulse">
                                                Loading Data...
                                            </span>
                                        </div>
                                    </td>
                                </tr> 
                            :
                                items.length == 0 
                                    ?
                                        <tr
                                        >
                                            <td 
                                                colSpan={columns.length}
                                                className='py-5 font-medium  text-center'
                                            >
                                                No Records Found
                                            </td>    
                                        </tr>
                                    :
                                        items.map(
                                            (item, index) =>
                                            (
                                                <WireTransferItemComponent
                                                    key={"ci-" + index}
                                                    item={item}
                                                    columns={columns}
                                                    handleClickNote={
                                                        () =>
                                                        {                                            
                                                            setStateSelectedItem(item);
                                                            setStateModalActiveNote(true);
                                                        }
                                                    }
                                                />
                                            )
                                        )
                    }
                </tbody>
            </table>

            {/* Pagination */}
            <CursorPaginate
                onFirstPage={onFirstPage}
                onLastPage={onLastPage}
                nextPageUrl={nextPageUrl}
                previousPageUrl={previousPageUrl}
                itemCount={itemCount}
                total={total}
                shouldPreserveState={true}
            />
        </div>
    );
}
