//* PACKAGES
import React, { useState, useEffect } from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
import { MdOutlineSettings, MdOutlineFilterAlt } from "react-icons/md";

//* COMPONENTS
import AppTabGroupComponent from '@/Components/App/AppTabGroupComponent';
import AdminLayout from "../../Layouts/AdminLayout";
import WireTransferFilter from "@/Components/WireTransfer/WireTransferFilter";
import WireTransferModalNoteComponent from '@/Components/WireTransfer/WireTransferModalNoteComponent';

//* PARTIALS
import PartialWireTransferSectionAddCredit from './Partials/PartialWireTransferSectionAddCredit';
import PartialWireTransferSectionMarketplacePayment from './Partials/PartialWireTransferSectionMarketplacePayment';
import PartialWireTransferSectionOfferPayment from './Partials/PartialWireTransferSectionOfferPayment';

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
import { getEventValue } from "@/Util/TargetInputEvent";

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function WireTransfeIndex(
    {
        items,
        onFirstPage,
        onLastPage,
        nextPageUrl,
        previousPageUrl,
        itemCount = 0,
        total = 0,
    }
)
{
    //! PACKAGE
    const paramOrderBy = route().params.orderBy;
    const paramCompany = route().params.company;
    const paramPurpose = route().params.purpose;

    //! HOOKS
    const { hasPermission } = usePermissions();

    //! VARIABLES
    const SORT_TYPE =
    {
        REFERENCE_NUMBER_DESC: "Reference Number:desc",
        REFERENCE_NUMBER_ASC : "Reference Number:asc",
        CLIENT_DESC          : "Client:desc",
        CLIENT_ASC           : "Client:asc",
        NAME_ASC             : "Name:asc",
        NAME_DESC            : "Name:desc",
        COMPANY_ASC          : "Company:asc",
        COMPANY_DESC         : "Company:desc",
        AMOUNT_ASC           : "Amount:asc",
        AMOUNT_DESC          : "Amount:desc",
        CREATED_ASC          : "Date Created:asc",
        CREATED_DESC         : "Date Created:desc",
        UPDATED_ASC          : "Date Updated:asc",
        UPDATED_DESC         : "Date Updated:desc",
    };

    //! STATES
    const [stateActiveTab, setStateActiveTab]             = useState(paramPurpose ?? 'addCredit')
    const [selectedItems, setSelectedItems]               = useState([]);
    const [stateShowItems, setStateShowItems]             = useState(route().params.showItems || 10);
    const [stateModalActiveNote, setStateModalActiveNote] = useState(false);
    const [stateSelectedItem, setStateSelectedItem]       = useState(null);
    
    const appTabItems = 
    {
        labels: 
        [
            {
                key         : 'addCredit',
                value       : 'Add Credit',
                isActive    : stateActiveTab == 'addCredit',
                eventOnClick: () =>
                {
                    router.get(
                        route('billing.wire.transfer'),
                        {
                            purpose: 'addCredit'
                        }, 
                        {
                            preserveState: true,
                            replace : true, 
                        }
                    );

                    setStateActiveTab('addCredit')
                }
            },    
            {
                key         : 'marketPlacePayment',
                value       : 'Marketplace Payment',
                isActive    : stateActiveTab == 'marketPlacePayment',
                eventOnClick: () =>
                {
                    router.get(
                        route('billing.wire.transfer'),
                        {
                            purpose: 'marketPlacePayment'
                        }, 
                        {
                            preserveState: true,
                            replace : true, 
                        }
                    );

                    setStateActiveTab('marketPlacePayment')
                }
            },    
            {
                key         : 'offerPayment',
                value       : 'offer payment',
                isActive    : stateActiveTab == 'offerPayment',
                eventOnClick: () =>
                {
                    router.get(
                        route('billing.wire.transfer'),
                        {
                            purpose: 'offerPayment'
                        }, 
                        {
                            preserveState: true,
                            replace : true, 
                        }
                    );
                    
                    setStateActiveTab('offerPayment')
                }
            },   
        ], 
        content: 
        [
            {
                key: 'addCredit',
                isActive: stateActiveTab == 'addCredit',
                value: <PartialWireTransferSectionAddCredit
                    items                   = {items}
                    onFirstPage             = {onFirstPage}
                    onLastPage              = {onLastPage}
                    nextPageUrl             = {nextPageUrl}
                    previousPageUrl         = {previousPageUrl}
                    itemCount               = {itemCount}
                    total                   = {total}
                    SORT_TYPE               = {SORT_TYPE}
                    paramOrderBy            = {paramOrderBy}
                    paramCompany            = {paramCompany}
                    selectedItems           = {selectedItems}
                    setSelectedItems        = {setSelectedItems}
                    stateSelectedItem       = {stateSelectedItem}
                    setStateSelectedItem    = {setStateSelectedItem}
                    setStateModalActiveNote = {setStateModalActiveNote}
                />
            }, 
            {
                key     : 'marketPlacePayment',
                isActive: stateActiveTab == 'marketPlacePayment',
                value   : <PartialWireTransferSectionMarketplacePayment
                    items                   = {items}
                    onFirstPage             = {onFirstPage}
                    onLastPage              = {onLastPage}
                    nextPageUrl             = {nextPageUrl}
                    previousPageUrl         = {previousPageUrl}
                    itemCount               = {itemCount}
                    total                   = {total}
                    SORT_TYPE               = {SORT_TYPE}
                    paramOrderBy            = {paramOrderBy}
                    paramCompany            = {paramCompany}
                    selectedItems           = {selectedItems}
                    setSelectedItems        = {setSelectedItems}
                    stateSelectedItem       = {stateSelectedItem}
                    setStateSelectedItem    = {setStateSelectedItem}
                    setStateModalActiveNote = {setStateModalActiveNote}
                />
            },  
            {
                key     : 'offerPayment',
                isActive: stateActiveTab == 'offerPayment',
                value   : <PartialWireTransferSectionOfferPayment
                    items                   = {items}
                    onFirstPage             = {onFirstPage}
                    onLastPage              = {onLastPage}
                    nextPageUrl             = {nextPageUrl}
                    previousPageUrl         = {previousPageUrl}
                    itemCount               = {itemCount}
                    total                   = {total}
                    SORT_TYPE               = {SORT_TYPE}
                    paramOrderBy            = {paramOrderBy}
                    paramCompany            = {paramCompany}
                    selectedItems           = {selectedItems}
                    setSelectedItems        = {setSelectedItems}
                    stateSelectedItem       = {stateSelectedItem}
                    setStateSelectedItem    = {setStateSelectedItem}
                    setStateModalActiveNote = {setStateModalActiveNote}
                />
            }
        ]
    };


    //! USE EFFECTS
    //...

    //! FUNCTIONS
    function handleshowItemsChange(e)
    {
        const value = getEventValue(e);
        
        setStateShowItems(value);
        
        router.get(
            route("billing.wire.transfer"),
            {
                ...route().params,
                showItems: value,
            },
            {
                replace      : true,
                preserveState: true
            }
        );
    };

    return (
        <AdminLayout>
            {/* hideNav={true} */}
            <WireTransferModalNoteComponent
                stateSelectedItem={stateSelectedItem}
                stateIsModalOpen={stateModalActiveNote}
                handleEventModalClose={() => {
                    setStateSelectedItem(null);
                    setStateModalActiveNote(false);
                }
                }
                handleEventModalConfirm={() => {
                    setStateSelectedItem(null);
                    setStateModalActiveNote(false);
                }
                }

            />

            <div
                className="mx-auto container max-w-[1200px] mt-5 flex flex-col gap-8 rounded-lg"
            >
        
                <div
                    className='flex justify-between'
                >
                    <div>
                        <div className='text-3xl font-semibold mb-3'>Wire Transfers</div>
                        <span className='text-gray-500 max-w-lg'>View & Manage Wire Transfers</span>
                    </div>
                </div>

                <div
                    className='flex flex-col gap-4'
                >
                    <div
                        className="flex justify-start"
                    >
                        <label className="mr-2 text-sm pt-1 text-gray-600">
                            Show
                        </label>
                        <select
                            value={stateShowItems}
                            onChange={handleshowItemsChange}
                            className="border border-gray-300 rounded px-4 py-1 text-sm w-20"
                        >
                            {[10, 20, 50, 100].map((val) => (
                                <option key={val} value={val}>
                                    {val}
                                </option>
                            ))}
                        </select>
                    </div>
                    <div
                        id="sample"
                        className="flex items-center space-x-2 flex-wrap min-h-[2rem]"
                    >
                        <label className="flex items-center">
                            <MdOutlineFilterAlt />
                            <span className="ml-2 text-sm text-gray-600">
                                {" "}
                                Filter:{" "}
                            </span>
                        </label>
                        <WireTransferFilter />
                    </div>
                </div>

                <AppTabGroupComponent
                    activeTab={stateActiveTab}
                    items={appTabItems}
                />
            </div>
        </AdminLayout>
    );
}
