<?php

use App\Http\Middleware\RoleChecker;
use App\Modules\MarketPlace\Controllers\MarketAuditsController;
use App\Modules\MarketPlace\Controllers\MarketDomainsController;
use App\Modules\MarketPlace\Controllers\MarketManualTransferController;
use App\Modules\MarketPlace\Controllers\MarketOfferController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'auth.active', 'auth.permission.check'])->prefix('market')->group(function () {
    Route::middleware([RoleChecker::class])->prefix('market')->group(function () {
        //
    });

    Route::get('/domains', [MarketDomainsController::class, 'index'])->name('domains');
    Route::post('/domains/get', [MarketDomainsController::class, 'paginate'])->name('domains.get');
    Route::post('/domains/update', [MarketDomainsController::class, 'update'])->name('domain.update');

    Route::get('/audits', [MarketAuditsController::class, 'index'])->name('audits');
    Route::post('/audits/resend', [MarketAuditsController::class, 'resendEmail'])->name('audit_resend_verification');
    Route::post('/audits/check', [MarketAuditsController::class, 'checkCode'])->name('audit_check');
    Route::get('/audits/export', [MarketAuditsController::class, 'exportCSV'])->name('audit_export');

    Route::get('/commissions', [MarketDomainsController::class, 'commissions'])->name('commissions');
    Route::post('/commissions/get', [MarketDomainsController::class, 'commpaginate'])->name('commissions.get');

    Route::get('/manuals', [MarketManualTransferController::class, 'index'])->name('market_manuals');
    Route::post('/manuals/auth', [MarketManualTransferController::class, 'store'])->name('market_manuals_auth');

    Route::get('/offers', [MarketOfferController::class, 'index'])->name('offers');
    Route::post('/offers/get', [MarketOfferController::class, 'getOffer'])->name('offers.get');
    Route::post('/offers/update', [MarketOfferController::class, 'update'])->name('offers.update');
    Route::post('/offers/history', [MarketOfferController::class, 'history'])->name('offers.history');
});
