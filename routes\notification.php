<?php

use App\Modules\Notification\Controllers\NotificationController;
use App\Modules\Notification\Controllers\GeneralNotificationController;
use App\Modules\Notification\Controllers\NotificationManagementController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'auth.active', 'registry.balance'])->prefix('notification')->group(function () {
    Route::get('/', [NotificationController::class, 'index'])->name('notification');
    Route::patch('/read-notif', [NotificationController::class, 'update'])->name('notification.update.read');
    Route::get('/unread-count', [NotificationController::class, 'getUnreadCount'])->name('notification.unread.count');
    Route::get('/dropdown-data', [NotificationController::class, 'getDropdownData'])->name('notification.dropdown.data');

    Route::middleware('auth.permission.check')
        ->group(
            function ()
            {
                // Route::get('/general', [GeneralNotificationController::class, 'index'])->name('notification.general');
                // Route::post('/general', [GeneralNotificationController::class, 'store'])->name('notification.general.store');
            
                // Route::get('/management', [NotificationManagementController::class, 'index'])->name('notification.management');
                // Route::post('/management/update-status', [NotificationManagementController::class, 'updateStatus'])->name('notification.management.update-status');
                // Route::post('/management/delete', [NotificationManagementController::class, 'delete'])->name('notification.management.delete');
                // Route::get('/management/users/{id}', [NotificationManagementController::class, 'users'])->name('notification.management.users');
            }
        );
});
