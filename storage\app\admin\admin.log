[2025-10-01 08:35:47] local.INFO: Domain History: Domain deletion request approved by admin 1 (a@a.a)  
[2025-10-01 08:35:48] local.INFO: Domain deletion request bypassing cron job waiting period for recently registered domain: fortysix.org (ID: 188) - Domain age is 0-5 days  
[2025-10-01 08:35:51] local.INFO: Starting domain deletion job for domain: fortysix.org (ID: 188) - Attempt 1/3  
[2025-10-01 08:35:57] local.INFO: Successfully completed domain deletion job for domain: fortysix.org  
[2025-10-02 07:35:53] local.INFO: user login from 127.0.0.1  
[2025-10-02 07:37:46] local.ERROR: {"query":[],"parameter":{"email":"<EMAIL>","disable_verification":false,"disable_deposit":false,"default_balance":false,"balance":0.1},"error":"Illuminate\\Validation\\ValidationException","message":"Error: Negative credit","code":0}  
[2025-10-02 07:37:55] local.ERROR: {"query":[],"parameter":{"email":"<EMAIL>","disable_verification":false,"disable_deposit":false,"default_balance":true,"balance":20,"errors":{"message":"Error: Negative credit"}},"error":"Illuminate\\Validation\\ValidationException","message":"This email address has already been invited.","code":0}  
[2025-10-02 07:38:01] local.ERROR: {"query":[],"parameter":{"email":"<EMAIL>","disable_verification":false,"disable_deposit":false,"default_balance":true,"balance":20,"errors":{"email":"This email address has already been invited."}},"error":"Illuminate\\Validation\\ValidationException","message":"This email address has already been invited.","code":0}  
[2025-10-02 07:38:06] local.ERROR: {"query":[],"parameter":{"email":"<EMAIL>","disable_verification":false,"disable_deposit":false,"default_balance":true,"balance":20,"errors":{"email":"This email address has already been invited."}},"error":"Illuminate\\Validation\\ValidationException","message":"The system credit is not sufficient to send an invitation.","code":0}  
[2025-10-02 07:38:31] local.ERROR: {"query":[],"parameter":{"email":"<EMAIL>","disable_verification":false,"disable_deposit":false,"default_balance":true,"balance":20,"errors":{"default_balance":"The system credit is not sufficient to send an invitation."}},"error":"Illuminate\\Validation\\ValidationException","message":"The system credit is not sufficient to send an invitation.","code":0}  
[2025-10-02 07:52:18] local.INFO: DomainRefundLimitReset: Running...  
[2025-10-02 07:52:18] local.INFO: Monthly counter reset completed. Records reset: 0  
[2025-10-02 07:52:18] local.INFO: DomainRefundLimitReset: Done  
[2025-10-02 07:52:29] local.INFO: DomainRefundLimitCalculator: Running...  
[2025-10-02 07:52:29] local.INFO: Monthly limit calculation completed. Total registrations: 0, New limit: 50, Records updated: 0  
[2025-10-02 07:52:29] local.INFO: DomainRefundLimitCalculator: Done  
[2025-10-02 07:58:56] local.INFO: DomainRefundLimitReset: Running...  
[2025-10-02 07:58:56] local.INFO: Monthly counter reset completed. Records reset: 0  
[2025-10-02 07:58:56] local.INFO: DomainRefundLimitReset: Done  
[2025-10-02 07:59:07] local.INFO: DomainRefundLimitCalculator: Running...  
[2025-10-02 07:59:07] local.INFO: Monthly limit calculation completed. Total registrations: 0, New limit: 50, Records updated: 0  
[2025-10-02 07:59:07] local.INFO: DomainRefundLimitCalculator: Done  
[2025-10-02 08:26:26] local.INFO: DomainRefundLimitCalculator: Running...  
[2025-10-02 08:26:26] local.INFO: Monthly limit calculation completed. Total registrations: 0, New limit: 50, Records updated: 0  
[2025-10-02 08:26:26] local.INFO: DomainRefundLimitCalculator: Done  
[2025-10-02 08:26:40] local.INFO: DomainRefundLimitReset: Running...  
[2025-10-02 08:26:40] local.INFO: Monthly counter reset completed. Records reset: 0  
[2025-10-02 08:26:40] local.INFO: DomainRefundLimitReset: Done  
[2025-10-02 08:31:03] local.INFO: DomainRefundLimitReset: Running...  
[2025-10-02 08:31:04] local.INFO: Monthly counter reset completed. Records reset: 1  
[2025-10-02 08:31:04] local.INFO: DomainRefundLimitReset: Done  
[2025-10-02 08:31:16] local.INFO: DomainRefundLimitCalculator: Running...  
[2025-10-02 08:31:16] local.INFO: Monthly limit calculation completed. Total registrations: 0, New limit: 50, Records updated: 1  
[2025-10-02 08:31:16] local.INFO: DomainRefundLimitCalculator: Done  
[2025-10-02 08:32:58] local.INFO: Domain History: Domain deletion request approved by admin 1 (a@a.a)  
[2025-10-02 08:32:58] local.INFO: Domain refund limit counter incremented. Count: 1, Limit: 50  
[2025-10-02 08:33:28] local.INFO: Domain History: Domain deletion request approved by admin 1 (a@a.a)  
[2025-10-03 01:19:41] local.INFO: DomainRefundLimitReset: Running...  
[2025-10-03 01:19:41] local.INFO: Monthly counter reset completed. Records reset: 1  
[2025-10-03 01:19:41] local.INFO: DomainRefundLimitReset: Done  
[2025-10-03 01:20:45] local.INFO: DomainRefundLimitCalculator: Running...  
[2025-10-03 01:20:45] local.INFO: Monthly limit calculation completed. Total registrations: 0, New limit: 50, Records updated: 1  
[2025-10-03 01:20:45] local.INFO: DomainRefundLimitCalculator: Done  
[2025-10-03 01:24:28] local.INFO: DomainRefundLimitMonthlyProcessor: Running monthly processing...  
[2025-10-03 01:24:28] local.INFO: DomainRefundLimitMonthlyProcessor: Resetting counters...  
[2025-10-03 01:24:28] local.INFO: Monthly counter reset completed. Records reset: 1  
[2025-10-03 01:24:28] local.INFO: DomainRefundLimitMonthlyProcessor: Calculating new limits...  
[2025-10-03 01:24:28] local.INFO: Monthly limit calculation completed. Total registrations: 0, New limit: 50, Records updated: 1  
[2025-10-03 01:24:28] local.INFO: DomainRefundLimitMonthlyProcessor: Monthly processing completed successfully  
[2025-10-03 06:01:37] local.INFO: user login from 127.0.0.1  
[2025-10-03 06:05:41] local.ERROR: {"query":[],"parameter":{"domainName":"twelvetoone.net","userEmail":"<EMAIL>","domainId":193,"createdDate":"2025-10-02 01:53:02","userId":7,"email":"<EMAIL>"},"error":"TypeError","message":"App\\Events\\DomainHistoryEvent::__construct(): Argument #1 ($data) must be of type array, int given, called in C:\\1xampp\\htdocs\\sd-admin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Events\\Dispatchable.php on line 14","code":0}  
[2025-10-03 06:05:45] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-10-03 06:08:02] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-10-03 06:08:26] local.INFO: Domain refund limit EXCEEDED. Count: 52, Limit: 50  
[2025-10-03 06:10:37] local.INFO: Starting domain deletion job for domain: twelvetoone.net (ID: 193) - Attempt 1/3  
[2025-10-03 06:10:50] local.INFO: Successfully completed domain deletion job for domain: twelvetoone.net  
[2025-10-03 07:34:21] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-10-03 08:18:03] local.INFO: Domain History: Domain deletion request approved by admin 1 (a@a.a)  
[2025-10-06 02:28:22] local.INFO: user login from 127.0.0.1  
[2025-10-06 02:28:56] local.INFO: Domain History: Domain deletion request cancelled by admin 1 (a@a.a)  
[2025-10-06 02:29:29] local.ERROR: HTTP request returned status code 400:
{"message":"cravytols.com found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"cravytols.com found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-10-06 02:29:29] local.ERROR: Domain cravytols.com not found in datastore. Response: {"status":{"message":"cravytols.com found but not available","status":"BAD_REQUEST","statusCode":400},"errors":"error"}  
[2025-10-06 02:29:29] local.ERROR: Domain reject request job failed for domain: cravytols.com (ID: 168). Error: Domain cravytols.com not found in datastore  
[2025-10-06 02:29:29] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Domain cravytols.com not found in datastore","code":0}  
[2025-10-06 02:29:30] local.ERROR: HTTP request returned status code 400:
{"message":"wallqueue.com found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"wallqueue.com found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-10-06 02:29:30] local.ERROR: Domain wallqueue.com not found in datastore. Response: {"status":{"message":"wallqueue.com found but not available","status":"BAD_REQUEST","statusCode":400},"errors":"error"}  
[2025-10-06 02:29:30] local.ERROR: Domain reject request job failed for domain: wallqueue.com (ID: 177). Error: Domain wallqueue.com not found in datastore  
[2025-10-06 02:29:30] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Domain wallqueue.com not found in datastore","code":0}  
[2025-10-06 02:29:30] local.ERROR: HTTP request returned status code 400:
{"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-10-06 02:29:30] local.ERROR: Domain klooty.net not found in datastore. Response: {"status":{"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400},"errors":"error"}  
[2025-10-06 02:29:30] local.ERROR: Domain reject request job failed for domain: klooty.net (ID: 173). Error: Domain klooty.net not found in datastore  
[2025-10-06 02:29:30] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Domain klooty.net not found in datastore","code":0}  
[2025-10-06 02:29:32] local.ERROR: HTTP request returned status code 400:
{"message":"cravytols.com found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"cravytols.com found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-10-06 02:29:32] local.ERROR: Domain cravytols.com not found in datastore. Response: {"status":{"message":"cravytols.com found but not available","status":"BAD_REQUEST","statusCode":400},"errors":"error"}  
[2025-10-06 02:29:32] local.ERROR: Domain reject request job failed for domain: cravytols.com (ID: 168). Error: Domain cravytols.com not found in datastore  
[2025-10-06 02:29:32] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Domain cravytols.com not found in datastore","code":0}  
[2025-10-06 02:29:32] local.ERROR: HTTP request returned status code 400:
{"message":"wallqueue.com found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"wallqueue.com found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-10-06 02:29:32] local.ERROR: Domain wallqueue.com not found in datastore. Response: {"status":{"message":"wallqueue.com found but not available","status":"BAD_REQUEST","statusCode":400},"errors":"error"}  
[2025-10-06 02:29:32] local.ERROR: Domain reject request job failed for domain: wallqueue.com (ID: 177). Error: Domain wallqueue.com not found in datastore  
[2025-10-06 02:29:32] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Domain wallqueue.com not found in datastore","code":0}  
[2025-10-06 02:29:33] local.ERROR: HTTP request returned status code 400:
{"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-10-06 02:29:33] local.ERROR: Domain klooty.net not found in datastore. Response: {"status":{"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400},"errors":"error"}  
[2025-10-06 02:29:33] local.ERROR: Domain reject request job failed for domain: klooty.net (ID: 173). Error: Domain klooty.net not found in datastore  
[2025-10-06 02:29:33] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Domain klooty.net not found in datastore","code":0}  
[2025-10-06 02:29:33] local.ERROR: HTTP request returned status code 400:
{"message":"cravytols.com found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"cravytols.com found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-10-06 02:29:33] local.ERROR: Domain cravytols.com not found in datastore. Response: {"status":{"message":"cravytols.com found but not available","status":"BAD_REQUEST","statusCode":400},"errors":"error"}  
[2025-10-06 02:29:33] local.ERROR: Domain reject request job failed for domain: cravytols.com (ID: 168). Error: Domain cravytols.com not found in datastore  
[2025-10-06 02:29:34] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Domain cravytols.com not found in datastore","code":0}  
[2025-10-06 02:29:34] local.ERROR: HTTP request returned status code 400:
{"message":"wallqueue.com found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"wallqueue.com found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-10-06 02:29:34] local.ERROR: Domain wallqueue.com not found in datastore. Response: {"status":{"message":"wallqueue.com found but not available","status":"BAD_REQUEST","statusCode":400},"errors":"error"}  
[2025-10-06 02:29:34] local.ERROR: Domain reject request job failed for domain: wallqueue.com (ID: 177). Error: Domain wallqueue.com not found in datastore  
[2025-10-06 02:29:34] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Domain wallqueue.com not found in datastore","code":0}  
[2025-10-06 02:29:35] local.ERROR: HTTP request returned status code 400:
{"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-10-06 02:29:35] local.ERROR: Domain klooty.net not found in datastore. Response: {"status":{"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400},"errors":"error"}  
[2025-10-06 02:29:35] local.ERROR: Domain reject request job failed for domain: klooty.net (ID: 173). Error: Domain klooty.net not found in datastore  
[2025-10-06 02:29:35] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Domain klooty.net not found in datastore","code":0}  
